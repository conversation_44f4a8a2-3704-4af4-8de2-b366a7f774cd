"use client";

import { useCallback } from "react";
import { useDispatch, useSelector } from "react-redux";
import {
  selectSettings,
  selectLayout,
  selectTheme,
  selectNotificationPreferences,
  setSettings,
  setLayout,
  setTheme,
  setNotificationPreferences,
  updateNotificationPreference,
  resetNotificationPreferences,
  type SettingsState,
  type NotificationPreferences,
} from "@/store/slices/settings";
import type { RootState } from "@/store";

export const usePreference = () => {
  const dispatch = useDispatch();
  const settings = useSelector((state: RootState) => selectSettings(state));
  const layout = useSelector((state: RootState) => selectLayout(state));
  const theme = useSelector((state: RootState) => selectTheme(state));
  const notificationPreferences = useSelector((state: RootState) =>
    selectNotificationPreferences(state)
  );

  const setPreferenceSettings = useCallback(
    (preference: Partial<SettingsState>) => {
      dispatch(setSettings(preference));
    },
    [dispatch]
  );

  const setPreferenceLayout = useCallback(
    (layout: "basic" | "advanced") => {
      dispatch(setLayout(layout));
    },
    [dispatch]
  );

  const setPreferenceTheme = useCallback(
    (theme: "light" | "dark") => {
      dispatch(setTheme(theme));
    },
    [dispatch]
  );

  const setPreferenceNotifications = useCallback(
    (preferences: NotificationPreferences) => {
      dispatch(setNotificationPreferences(preferences));
    },
    [dispatch]
  );

  const updatePreferenceNotification = useCallback(
    (category: keyof NotificationPreferences, key: string, value: boolean) => {
      dispatch(updateNotificationPreference({ category, key, value }));
    },
    [dispatch]
  );

  const resetPreferenceNotifications = useCallback(() => {
    dispatch(resetNotificationPreferences());
  }, [dispatch]);

  return {
    // Full settings object
    settings,

    // Individual settings
    layout,
    theme,
    notificationPreferences,

    // Setters
    setPreferenceSettings,
    setPreferenceLayout,
    setPreferenceTheme,
    setPreferenceNotifications,
    updatePreferenceNotification,
    resetPreferenceNotifications,
  };
};
