"use client";

import useS<PERSON> from "swr";

import { useRouter, useParams } from "next/navigation";

import { fetcher } from "@/lib/common/requests";
import { toast } from "sonner";
import { useCallback, useEffect } from "react";
import { useSelector, useDispatch } from "react-redux";
import { RootState, AppDispatch } from "@/store";
import {
  fetchProposal,
  createProposal,
  updateProposal,
  deleteProposal,
} from "@/store/actions/proposals";
import {
  selectCurrentProposal,
  selectIsCreating,
  selectIsUpdating,
  selectIsDeleting,
  selectError,
  clearError,
  setCreating,
  setUpdating,
  setDeleting,
  setError,
  setCurrentProposal,
} from "@/store/slices/proposal";
import type {
  CreateProposal,
  UpdateProposal,
  Proposal,
} from "@/lib/api/validators/schemas/proposal";
import type { ProposalStats } from "@/data/proposals-mock";
import { usePagination } from "./usePagination";

export function useProposal() {
  const router = useRouter();
  const { slug } = useParams();

  const dispatch = useDispatch<AppDispatch>();

  // Initialize pagination hook for proposals table
  const paginationHook = usePagination("proposals-table");

  // Redux selectors for operation states
  const currentProposal = useSelector((state: RootState) =>
    selectCurrentProposal(state)
  );
  const isCreating = useSelector((state: RootState) => selectIsCreating(state));
  const isUpdating = useSelector((state: RootState) => selectIsUpdating(state));
  const isDeleting = useSelector((state: RootState) => selectIsDeleting(state));
  const error = useSelector((state: RootState) => selectError(state));

  // SWR hooks for data fetching
  const {
    data: proposalsData,
    error: proposalsError,
    isLoading: isLoadingProposals,
    mutate: mutateProposals,
  } = useSWR("proposal", fetcher);

  const {
    data: statisticsData,
    error: statisticsError,
    isLoading: isLoadingStats,
    mutate: mutateStatistics,
  } = useSWR("proposal/statistics", fetcher);

  // Extract data from SWR responses
  const proposals: Proposal[] =
    proposalsData?.data.proposals || proposalsData?.data || [];
  const serverPagination = proposalsData?.pagination;
  const statistics: ProposalStats | null = statisticsData?.data || null;
  const isLoading = isLoadingProposals;

  // Initialize and update pagination based on fetched data
  useEffect(() => {
    if (proposalsData) {
      // Initialize pagination if not already done
      if (paginationHook.pagination.total === 0) {
        paginationHook.initPagination({
          initialPage: serverPagination?.page || 1,
          initialLimit: serverPagination?.limit || 10,
          initialOrderBy: { createdAt: "desc" },
        });
      }

      // Update pagination with server data when available
      if (serverPagination) {
        paginationHook.updateTotal(serverPagination.total);
        // Update other pagination metadata if needed
        if (serverPagination.page !== paginationHook.pagination.page) {
          paginationHook.setPage(serverPagination.page);
        }
        if (serverPagination.limit !== paginationHook.pagination.limit) {
          paginationHook.setLimit(serverPagination.limit);
        }
      }
    }
  }, [proposalsData, serverPagination, paginationHook]);

  // Handle SWR errors
  const swrError = proposalsError || statisticsError;
  if (swrError && !error) {
    dispatch(setError(swrError.message));
  }

  // API operations
  const create = useCallback(
    async (data: CreateProposal) => {
      try {
        dispatch(setCreating(true));
        dispatch(clearError());

        const result = await dispatch(createProposal(data)).unwrap();

        // Show success toast
        toast.success("Proposal created successfully");

        // Refresh data after creation
        await mutateProposals();
        await mutateStatistics();

        return result;
      } catch (error) {
        const errorMessage =
          error instanceof Error ? error.message : "Failed to create proposal";
        toast.error(errorMessage);
        dispatch(setError(errorMessage));
        throw error;
      } finally {
        dispatch(setCreating(false));
      }
    },
    [dispatch, mutateProposals, mutateStatistics]
  );

  const update = useCallback(
    async (data: UpdateProposal) => {
      try {
        dispatch(setUpdating(true));
        dispatch(clearError());

        const result = await dispatch(updateProposal(data)).unwrap();

        // Show success toast
        toast.success("Proposal updated successfully");

        // Update current proposal if it's the one being updated
        if (currentProposal?.id === data.id) {
          dispatch(setCurrentProposal(result));
        }

        // Refresh data after update
        await mutateProposals();
        await mutateStatistics();

        return result;
      } catch (error) {
        const errorMessage =
          error instanceof Error ? error.message : "Failed to update proposal";
        toast.error(errorMessage);
        dispatch(setError(errorMessage));
        throw error;
      } finally {
        dispatch(setUpdating(false));
      }
    },
    [dispatch, currentProposal, mutateProposals, mutateStatistics]
  );

  const remove = useCallback(
    async (id: string) => {
      try {
        dispatch(setDeleting(true));
        dispatch(clearError());

        await dispatch(deleteProposal(id)).unwrap();

        // Show success toast
        toast.success("Proposal deleted successfully");

        router.push(`/${slug}/proposals`);

        // Clear current proposal if it's the one being deleted
        if (currentProposal?.id === id) {
          dispatch(setCurrentProposal(null));
        }

        // Refresh data after deletion
        await mutateProposals();
        await mutateStatistics();
      } catch (error) {
        const errorMessage =
          error instanceof Error ? error.message : "Failed to delete proposal";
        toast.error(errorMessage);
        dispatch(setError(errorMessage));
        throw error;
      } finally {
        dispatch(setDeleting(false));
      }
    },
    [dispatch, currentProposal, mutateProposals, mutateStatistics]
  );

  // Utility functions
  const fetchById = useCallback(
    async (id: string) => {
      try {
        const result = await dispatch(fetchProposal(id)).unwrap();
        dispatch(setCurrentProposal(result));
        return result;
      } catch (error) {
        const errorMessage =
          error instanceof Error ? error.message : "Failed to fetch proposal";
        dispatch(setError(errorMessage));
        throw error;
      }
    },
    [dispatch]
  );

  const clearErrorState = useCallback(() => {
    dispatch(clearError());
  }, [dispatch]);

  return {
    // State
    proposals,
    currentProposal,
    statistics,
    isLoading,
    isCreating,
    isUpdating,
    isDeleting,
    isLoadingStats,
    error,

    // Pagination
    pagination: paginationHook,

    // Actions
    create,
    fetchById,
    update,
    remove,
    clearError: clearErrorState,

    // SWR utilities
    mutateProposals,
    mutateStatistics,

    // Computed values
    hasProposals: proposals.length > 0,
    isAnyLoading:
      isLoading || isCreating || isUpdating || isDeleting || isLoadingStats,
  };
}
