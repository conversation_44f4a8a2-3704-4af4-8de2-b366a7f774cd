import { useCallback } from "react";
import { useRB<PERSON> as useRB<PERSON><PERSON>ontext } from "@/lib/rbac/context";
import { useAuth } from "@/hooks/useAuth";
import { EntityType, PermissionAction } from "@/lib/rbac/types";
import { createPermissionId, parsePermissionId } from "@/lib/rbac";
import type { Profile } from "@/store/actions/auth";
import { toast } from "sonner";

/**
 * Main RBAC hook that provides access to RBAC context
 * Used by RBACWrapper and other components for permission validation
 */
export function useRBAC() {
  // Use the RBAC context instead of direct Redux selectors
  return useRBACContext();
}

// Hook for permission checking using auth state
export function usePermissions() {
  const { user: currentUser } = useAuth();

  // Entity-based permission checking (new format)
  const hasEntityPermission = useCallback(
    (entity: string, action: string): boolean => {
      if (!currentUser?.role?.permissions) return false;
      const entityPermissions = (currentUser.role.permissions as any)[entity];
      return entityPermissions ? entityPermissions.includes(action) : false;
    },
    [currentUser]
  );

  // Legacy string-based permission checking (backward compatibility)
  const hasPermission = useCallback(
    (permission: string): boolean => {
      if (!currentUser?.role?.permissions) return false;

      // Try to parse as entity:action format
      const parsed = parsePermissionId(permission);
      if (parsed) {
        return hasEntityPermission(parsed.entity, parsed.action);
      }

      // Fallback: check if permissions is still array format (legacy)
      if (Array.isArray(currentUser.role.permissions)) {
        return currentUser.role.permissions.includes(permission);
      }

      return false;
    },
    [currentUser, hasEntityPermission]
  );

  const hasAnyPermission = useCallback(
    (permissions: string[]): boolean => {
      if (!currentUser?.role?.permissions) return false;
      return permissions.some((permission) => hasPermission(permission));
    },
    [currentUser, hasPermission]
  );

  const hasAllPermissions = useCallback(
    (permissions: string[]): boolean => {
      if (!currentUser?.role?.permissions) return false;
      return permissions.every((permission) => hasPermission(permission));
    },
    [currentUser, hasPermission]
  );

  // Entity-based permission checking for multiple permissions
  const hasAnyEntityPermission = useCallback(
    (entity: string, actions: string[]): boolean => {
      return actions.some((action) => hasEntityPermission(entity, action));
    },
    [hasEntityPermission]
  );

  const hasAllEntityPermissions = useCallback(
    (entity: string, actions: string[]): boolean => {
      return actions.every((action) => hasEntityPermission(entity, action));
    },
    [hasEntityPermission]
  );

  // Get user permissions in different formats
  const getUserPermissions = useCallback((): any => {
    return currentUser?.role?.permissions || {};
  }, [currentUser]);

  const getUserPermissionsAsArray = useCallback((): string[] => {
    const permissions = currentUser?.role?.permissions;
    if (!permissions) return [];

    // If it's already an array (legacy format), return as is
    if (Array.isArray(permissions)) return permissions;

    // Convert entity-based object to string array
    const permissionArray: string[] = [];
    Object.entries(permissions).forEach(([entity, actions]) => {
      if (Array.isArray(actions)) {
        actions.forEach((action) => {
          permissionArray.push(createPermissionId(entity, action));
        });
      }
    });
    return permissionArray;
  }, [currentUser]);

  return {
    // Legacy functions (backward compatibility)
    hasPermission,
    hasAnyPermission,
    hasAllPermissions,
    getUserPermissions,
    getUserPermissionsAsArray,

    // New entity-based functions
    hasEntityPermission,
    hasAnyEntityPermission,
    hasAllEntityPermissions,

    // User data
    userRole: currentUser?.role,
    currentUser,
  };
}

/**
 * Hook for checking multiple permissions at once
 */
export function useMultiplePermissions(
  checks: Array<{
    entity: EntityType;
    action: PermissionAction;
    resourceId?: string;
  }>
) {
  const { hasEntityPermission } = usePermissions();

  const results = checks.map((check) => ({
    ...check,
    granted: hasEntityPermission(check.entity, check.action),
  }));

  const allGranted = results.every((result) => result.granted);
  const anyGranted = results.some((result) => result.granted);

  return {
    results,
    allGranted,
    anyGranted,
  };
}

/**
 * Hook for entity-specific permissions (all CRUD operations)
 */
export function useEntityPermissions(entity: EntityType, resourceId?: string) {
  const { hasEntityPermission } = usePermissions();

  return {
    canCreate: hasEntityPermission(entity, "create"),
    canRead: hasEntityPermission(entity, "read"),
    canUpdate: hasEntityPermission(entity, "update"),
    canDelete: hasEntityPermission(entity, "delete"),

    // Convenience methods
    hasFullAccess:
      hasEntityPermission(entity, "create") &&
      hasEntityPermission(entity, "read") &&
      hasEntityPermission(entity, "update") &&
      hasEntityPermission(entity, "delete"),
    hasReadOnly:
      hasEntityPermission(entity, "read") &&
      !hasEntityPermission(entity, "create") &&
      !hasEntityPermission(entity, "update") &&
      !hasEntityPermission(entity, "delete"),
  };
}

/**
 * Hook for managing user profiles
 * Uses auth system and new updateUserProfile action
 */
export function useUserProfile() {
  const { user: currentUser, isLoading: isAuthLoading } = useAuth();

  const profile = currentUser?.profile;

  const updateUserProfile = useCallback(
    async (profileData: Partial<Profile>) => {
      try {
        // Import the action dynamically to avoid circular dependencies
        const { updateUserProfile: updateProfileAction } = await import(
          "@/store/actions/auth"
        );
        const { useDispatch } = await import("react-redux");
        const dispatch = useDispatch();

        const result = await dispatch(updateProfileAction(profileData) as any);

        // Handle toast notifications based on result
        if (result.type.endsWith("/fulfilled")) {
          toast.success("Profile updated successfully");
          return { success: true, data: result.payload };
        } else if (result.type.endsWith("/rejected")) {
          const errorMessage =
            result.payload ||
            result.error?.message ||
            "Failed to update profile";
          toast.error(errorMessage);
          return { success: false, error: errorMessage };
        }

        return result;
      } catch (error: any) {
        const errorMessage = error?.message || "Failed to update profile";
        toast.error(errorMessage);
        return { success: false, error: errorMessage };
      }
    },
    []
  );

  return {
    profile,
    currentUser,
    isLoading: isAuthLoading,
    error: null,
    updateUserProfile,
    upsertProfile: updateUserProfile, // Alias for backward compatibility
    hasProfile: !!profile,
  };
}
