"use client";

import useS<PERSON> from "swr";
import { api } from "@/lib/common/requests";
import { useCallback, useEffect } from "react";
import { useSelector, useDispatch } from "react-redux";
import type { RootState, AppDispatch } from "@/store";
import {
  clearError,
  setCurrentDocument,
  clearCurrentDocument,
  setCreating,
  setUpdating,
  setDeleting,
  setError,
} from "@/store/slices/document";
import { fetchSignedURL, bulkCreateDocuments } from "@/store/actions/documents";
import type {
  Document,
  CreateDocument,
  UpdateDocument,
} from "@/lib/api/validators/schemas/document";
import { usePagination } from "./usePagination";

// Fetcher function for SWR using api module
const fetcher = async (url: string) => {
  try {
    // Remove '/api/' prefix since api module already includes it
    const endpoint = url.startsWith("/api/") ? url.slice(5) : url;
    return await api.get(endpoint);
  } catch (error) {
    console.error("Fetcher error:", error);
    throw error;
  }
};

export function useDocuments() {
  const dispatch = useDispatch<AppDispatch>();

  // Initialize pagination hook for documents table
  const paginationHook = usePagination("documents-table");

  // Redux selectors for operation states
  const currentDocument = useSelector(
    (state: RootState) => state.documents.currentDocument
  );
  const isCreating = useSelector(
    (state: RootState) => state.documents.isCreating
  );
  const isUpdating = useSelector(
    (state: RootState) => state.documents.isUpdating
  );
  const isDeleting = useSelector(
    (state: RootState) => state.documents.isDeleting
  );
  const error = useSelector((state: RootState) => state.documents.error);

  // SWR hooks for data fetching
  const {
    data: documentsData,
    error: documentsError,
    isLoading: isLoadingDocuments,
    mutate: mutateDocuments,
  } = useSWR("document", fetcher);

  const {
    data: statisticsData,
    error: statisticsError,
    isLoading: isLoadingStats,
    mutate: mutateStatistics,
  } = useSWR("document/statistics", fetcher);

  // Extract data from SWR responses
  const documents: Document[] =
    documentsData?.data?.documents || documentsData?.data || [];
  const serverPagination = documentsData?.pagination;
  const statistics = statisticsData?.data || null;
  const isLoading = isLoadingDocuments;

  // Initialize and update pagination based on fetched data
  useEffect(() => {
    if (documentsData) {
      // Initialize pagination if not already done
      if (paginationHook.pagination.total === 0) {
        paginationHook.initPagination({
          initialPage: serverPagination?.page || 1,
          initialLimit: serverPagination?.limit || 15,
          initialOrderBy: { createdAt: "desc" },
        });
      }

      // Update pagination with server data when available
      if (serverPagination) {
        paginationHook.updateTotal(serverPagination.total);
        // Update other pagination metadata if needed
        if (serverPagination.page !== paginationHook.pagination.page) {
          paginationHook.setPage(serverPagination.page);
        }
        if (serverPagination.limit !== paginationHook.pagination.limit) {
          paginationHook.setLimit(serverPagination.limit);
        }
      }
    }
  }, [documentsData, serverPagination]);

  // Handle SWR errors
  const swrError = documentsError || statisticsError;
  if (swrError && !error) {
    dispatch(setError(swrError.message));
  }

  const fetchSignedDocumentURL = async (path: string) => {
    try {
      return await dispatch(fetchSignedURL(path)).unwrap();
    } catch (error: any) {
      return error;
    }
  };

  // API operations
  const create = useCallback(
    async (documentData: FormData) => {
      try {
        dispatch(setCreating(true));
        dispatch(clearError());

        // Check if documentData is FormData for file uploads, otherwise use regular post
        const result = await api.upload("document", documentData);

        // Refresh data after creation
        await mutateDocuments();
        await mutateStatistics();

        return result;
      } catch (error) {
        const errorMessage =
          error instanceof Error ? error.message : "Failed to create document";
        dispatch(setError(errorMessage));
        throw error;
      } finally {
        dispatch(setCreating(false));
      }
    },
    [dispatch, mutateDocuments, mutateStatistics]
  );

  const getSignedURL = (path: string) => {
    return "";
  };

  const update = useCallback(
    async (id: string, documentData: Partial<UpdateDocument>) => {
      try {
        dispatch(setUpdating(true));
        dispatch(clearError());

        const result = await api.put(`document/${id}`, {
          id,
          ...documentData,
        });

        // Update current document if it's the one being updated
        if (currentDocument?.id === id) {
          dispatch(setCurrentDocument(result));
        }

        // Refresh data after update
        await mutateDocuments();
        await mutateStatistics();

        return result;
      } catch (error) {
        const errorMessage =
          error instanceof Error ? error.message : "Failed to update document";
        dispatch(setError(errorMessage));
        throw error;
      } finally {
        dispatch(setUpdating(false));
      }
    },
    [dispatch, currentDocument, mutateDocuments, mutateStatistics]
  );

  const remove = useCallback(
    async (id: string) => {
      try {
        dispatch(setDeleting(true));
        dispatch(clearError());

        await api.delete(`document/${id}`);

        // Clear current document if it's the one being deleted
        if (currentDocument?.id === id) {
          dispatch(setCurrentDocument(null));
        }

        // Refresh data after deletion
        await mutateDocuments();
        await mutateStatistics();

        return { success: true };
      } catch (error) {
        const errorMessage =
          error instanceof Error ? error.message : "Failed to delete document";
        dispatch(setError(errorMessage));
        throw error;
      } finally {
        dispatch(setDeleting(false));
      }
    },
    [dispatch, currentDocument, mutateDocuments, mutateStatistics]
  );

  // Utility functions
  const fetchById = useCallback(
    async (documentId: string) => {
      try {
        const result = await api.get(`document/${documentId}`);
        if (result.success && !result.error) {
          dispatch(setCurrentDocument(result.data || result));
          return result.data || result;
        } else {
          throw new Error(result.message || "Failed to fetch document");
        }
      } catch (error) {
        const errorMessage =
          error instanceof Error ? error.message : "Failed to fetch document";
        dispatch(setError(errorMessage));
        throw error;
      }
    },
    [dispatch]
  );

  const bulkDocuments = useCallback((data: any) => {
    try {
      bulkCreateDocuments(data);
    } catch (error: any) {
      console.error("Failed to bulk create documents:", error);
      dispatch(setError(error.message));
      throw error;
    }
  }, []);

  const clearErrorState = useCallback(() => {
    dispatch(clearError());
  }, [dispatch]);

  const setDocument = useCallback(
    (document: Document | null) => {
      dispatch(setCurrentDocument(document));
    },
    [dispatch]
  );

  const clearDocument = useCallback(() => {
    dispatch(clearCurrentDocument());
  }, [dispatch]);

  // Helper function to get file extension
  const getFileExtension = (fileName: string): string => {
    const extension = fileName.split(".").pop()?.toLowerCase();
    return extension || "pdf";
  };

  const getExtensionType = (fileName: string): string => {
    const extension = fileName.split(".").pop()?.toLowerCase();
    switch (extension) {
      case "pdf":
      case "doc":
      case "docx":
      case "xls":
      case "xlsx":
        return "application";
      case "jpg":
      case "jpeg":
      case "png":
      case "gif":
        return "image";
      default:
        return "other";
    }
  };

  // Download document
  const downloadDocument = useCallback(
    async (doc: any) => {
      try {
        // Fetch the document first to get the download URL or file data
        if (doc && doc.path) {
          const link = document.createElement("a");
          const extension = getFileExtension(doc.name);
          const extensionType = getExtensionType(doc.name);

          link.href = `data://${extensionType}/${extension},` + doc.path;
          link.target = "_blank";
          link.download = doc.name;

          document.body.appendChild(link);
          link.click();
          document.body.removeChild(link);
        } else {
          throw new Error("Document file URL not available");
        }
      } catch (error) {
        console.error("Failed to download document:", error);
        throw error;
      }
    },
    [fetchById]
  );

  return {
    // State
    documents,
    currentDocument,
    statistics,
    isLoading,
    isCreating,
    isUpdating,
    isDeleting,
    isLoadingStats,
    error,

    // Pagination
    pagination: paginationHook,

    // Actions
    create,
    fetchById,
    update,
    remove,
    bulkDocuments,
    downloadDocument,
    fetchSignedURL: fetchSignedDocumentURL,
    clearError: clearErrorState,
    setCurrentDocument: setDocument,
    clearCurrentDocument: clearDocument,

    // SWR utilities
    mutateDocuments,
    mutateStatistics,

    // Computed values
    hasDocuments: documents.length > 0,
    documentsCount: documents.length,
    isAnyLoading:
      isLoading || isCreating || isUpdating || isDeleting || isLoadingStats,
  };
}
