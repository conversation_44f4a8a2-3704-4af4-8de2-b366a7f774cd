"use client";

import { useCallback } from "react";
import { usePreference } from "./usePreference";

export type LayoutType = "basic" | "advanced";

export interface LayoutSettings {
  layout: LayoutType;
  // Add other layout-related settings here
  viewMode?: "table" | "grid" | "list";
  showToolbar?: boolean;
  enableSearch?: boolean;
  enableFilters?: boolean;
}

export const useLayout = () => {
  const { settings, setPreferenceSettings } = usePreference();

  // Get current layout type
  const layoutType: LayoutType = settings?.layout || "basic";

  // Check if current layout is basic
  const isBasic = layoutType === "basic";

  // Check if current layout is advanced
  const isAdvanced = layoutType === "advanced";

  // Switch to basic layout
  const switchToBasic = useCallback(() => {
    setPreferenceSettings({
      ...settings,
      layout: "basic" as LayoutType,
    });
  }, [settings, setPreferenceSettings]);

  // Switch to advanced layout
  const switchToAdvanced = useCallback(() => {
    setPreferenceSettings({
      ...settings,
      layout: "advanced" as LayoutType,
    });
  }, [settings, setPreferenceSettings]);

  // Toggle between layouts
  const toggleLayout = useCallback(() => {
    const newLayout = layoutType === "basic" ? "advanced" : "basic";
    setPreferenceSettings({
      ...settings,
      layout: newLayout,
    });
  }, [layoutType, settings, setPreferenceSettings]);

  // Set layout type
  const setLayout = useCallback((layout: LayoutType) => {
    setPreferenceSettings({
      ...settings,
      layout,
    });
  }, [settings, setPreferenceSettings]);

  // Update layout settings
  const updateLayoutSettings = useCallback((newSettings: Partial<LayoutSettings>) => {
    setPreferenceSettings({
      ...settings,
      ...newSettings,
    });
  }, [settings, setPreferenceSettings]);

  // Get layout-specific capabilities
  const capabilities = {
    hasToolbar: isAdvanced,
    hasDynamicIcons: isAdvanced,
    hasAdvancedFilters: isAdvanced,
    hasViewModeToggle: isAdvanced,
    hasHeader: isBasic,
    hasFilters: isBasic,
    hasControls: isBasic,
  };

  return {
    // Current state
    layoutType,
    isBasic,
    isAdvanced,
    settings: settings as LayoutSettings,
    capabilities,

    // Actions
    switchToBasic,
    switchToAdvanced,
    toggleLayout,
    setLayout,
    updateLayoutSettings,
  };
};

export default useLayout;
