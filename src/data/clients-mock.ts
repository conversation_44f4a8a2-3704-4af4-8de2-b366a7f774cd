export interface Client {
  id: string;
  email: string;
  name?: string;
  interest?: string[];
  createdAt: string;
  updatedAt: string;
}

export const mockClients: Client[] = [
  {
    id: "client-1",
    email: "<EMAIL>",
    name: "<PERSON>",
    interest: ["newsletter"],
    createdAt: "2024-01-10T10:00:00.000Z",
    updatedAt: "2024-01-15T14:30:00.000Z",
  },
  {
    id: "client-2", 
    email: "<EMAIL>",
    name: "<PERSON>",
    interest: ["wishlist", "newsletter"],
    createdAt: "2024-01-12T09:00:00.000Z",
    updatedAt: "2024-01-18T16:45:00.000Z",
  },
  {
    id: "client-3",
    email: "<EMAIL>",
    name: "<PERSON>",
    interest: ["wishlist"],
    createdAt: "2024-01-14T11:00:00.000Z",
    updatedAt: "2024-01-20T13:20:00.000Z",
  },
  {
    id: "client-4",
    email: "<EMAIL>",
    name: "<PERSON>",
    interest: ["newsletter"],
    createdAt: "2024-01-16T08:30:00.000Z",
    updatedAt: "2024-01-22T10:15:00.000Z",
  },
  {
    id: "client-5",
    email: "<EMAIL>",
    name: "David Brown",
    interest: ["wishlist", "newsletter"],
    createdAt: "2024-01-18T14:00:00.000Z",
    updatedAt: "2024-01-24T12:45:00.000Z",
  },
];
