"use server";

import { auth } from "@/lib/auth";
import { NextRequest, NextResponse } from "next/server";
import { ProposalService } from "@/lib/api/services/proposal";

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Initialize proposal service
    const session: any = await auth();
    const proposalService = new ProposalService({
      requireAuth: true,
    });

    // Set the service context with session and request
    proposalService.setContext({
      session: session || undefined,
      user: session?.user,
      request,
    });

    // Use the service to get proposals
    const result = await proposalService.getProposals({ id: params.id });

    return NextResponse.json(result, { status: 200 });
  } catch (error) {
    console.error("Proposal GET error:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}

export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Initialize proposal service
    const session: any = await auth();
    const proposalService = new ProposalService({
      requireAuth: true,
    });

    // Set the service context with session and request
    proposalService.setContext({
      session: session || undefined,
      user: session?.user,
      request,
    });

    // Extract request body
    const body = await request.json();
    body.id = params.id;

    // Use the service to update proposal
    const result = await proposalService.updateProposal(body);

    return NextResponse.json(result, { status: 200 });
  } catch (error) {
    console.error("Proposal UPDATE error:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}

export async function PATCH(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Initialize proposal service
    const session: any = await auth();
    const proposalService = new ProposalService({
      requireAuth: true,
    });

    // Set the service context with session and request
    proposalService.setContext({
      session: session || undefined,
      user: session?.user,
      request,
    });

    // Extract request body
    const body = await request.json();
    body.id = params.id;

    // Use the service to update proposal
    const result = await proposalService.updateProposal(body);

    return NextResponse.json(result, { status: 200 });
  } catch (error) {
    console.error("Proposal UPDATE error:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Initialize proposal service
    const session: any = await auth();
    const proposalService = new ProposalService({
      requireAuth: true,
    });

    // Set the service context with session and request
    proposalService.setContext({
      session,
      user: session?.user,
      request,
    });

    // Use the service to delete proposal
    const result = await proposalService.deleteProposal(params.id);

    return NextResponse.json(result, { status: 200 });
  } catch (error) {
    console.error("Proposal DELETE error:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}
