"use server";

import { NextRequest, NextResponse } from "next/server";
import { auth } from "@/lib/auth";
import { ChatService } from "@/lib/api/services/chat";

/**
 * PUT /api/chat/user-state
 * Update user state (online/offline/typing/away)
 */
export async function PUT(request: NextRequest) {
  try {
    // Initialize chat service
    const session: any = await auth();
    const chatService = new ChatService({
      requiredAuth: true,
    });

    // Set the service context with session and request
    chatService.setContext({
      session: session || undefined,
      user: session?.user,
      request,
    });

    // Parse request body
    const { userId, state, roomId } = await request.json();

    // Use current user ID if not provided
    const targetUserId = userId || session?.user?.id;

    if (!targetUserId) {
      return NextResponse.json(
        {
          success: false,
          error: true,
          message: "User ID is required",
          timestamp: new Date().toISOString(),
        },
        { status: 400 }
      );
    }

    // Use the service to update user state
    const result = await chatService.updateUserState(
      targetUserId,
      state,
      roomId
    );

    return NextResponse.json(result, {
      status: result.success ? 200 : result.statusCode || 500,
    });
  } catch (error) {
    console.error("Chat user state PUT error:", error);
    return NextResponse.json(
      {
        success: false,
        error: true,
        message: "Internal server error",
        timestamp: new Date().toISOString(),
      },
      { status: 500 }
    );
  }
}
