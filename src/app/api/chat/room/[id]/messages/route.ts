"use server";

import { NextRequest, NextResponse } from "next/server";
import { auth } from "@/lib/auth";
import { ChatService } from "@/lib/api/services/chat";

/**
 * GET /api/chat/rooms/[id]/messages
 * Get messages for a specific room
 */
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Initialize chat service
    const session: any = await auth();
    const chatService = new ChatService({
      requiredAuth: true,
    });

    // Set the service context with session and request
    chatService.setContext({
      session: session || undefined,
      user: session?.user,
      request,
    });

    // Parse params
    let param = await params;

    // Extract query parameters
    const { searchParams } = new URL(request.url);
    const limit = parseInt(searchParams.get("limit") || "50");
    const offset = parseInt(searchParams.get("offset") || "0");

    // Use the service to get messages
    const result = await chatService.getMessages(param.id, limit, offset);

    return NextResponse.json(result, {
      status: result.success ? 200 : result.statusCode || 500,
    });
  } catch (error) {
    console.error("Chat messages GET error:", error);
    return NextResponse.json(
      {
        success: false,
        error: true,
        message: "Internal server error",
        timestamp: new Date().toISOString(),
      },
      { status: 500 }
    );
  }
}

/**
 * POST /api/chat/rooms/[id]/messages
 * Send a message to a specific room
 */
export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Initialize chat service
    const session: any = await auth();
    const chatService = new ChatService({
      requiredAuth: true,
    });

    // Set the service context with session and request
    chatService.setContext({
      session: session || undefined,
      user: session?.user,
      request,
    });

    let { searchParams } = new URL(request.url);
    const withAttachments = searchParams.get("withAttachments") === "true";

    let messageData: any;

    // Parse request body
    if (withAttachments) {
      const formData = await request.formData();
      messageData = Object.fromEntries((formData as any).entries());
    } else {
      messageData = await request.json();
    }

    // Use the service to send message
    const result = await chatService.sendMessage(messageData);

    return NextResponse.json(result, {
      status: result.success ? 201 : result.statusCode || 500,
    });
  } catch (error: any) {
    console.error("Chat message POST error:", error);
    return NextResponse.json(error, { status: 500 });
  }
}
