"use client";

import React, { useEffect, useState, useRef } from "react";
import { useChat } from "@/hooks/useChat";
import { RoomList } from "@/components/view/chat/room-list";
import { MessageList } from "@/components/view/chat/message-list";
import { MessageComposer } from "@/components/view/chat/message-composer";
import { But<PERSON> } from "@/components/common/ui/button";
import { CreateRoomDialog, EditRoomDialog } from "@/components/view/chat";
import { ContractDetailsDialog } from "@/components/view/chat/contract-details-dialog";

import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/common/ui/dropdown-menu";

import {
  MessageCircle,
  Users,
  Phone,
  Video,
  MoreVertical,
  Hash,
  FileText,
  LogOut,
} from "lucide-react";
import { cn } from "@/lib/utils";
import {
  PanelResize<PERSON>andle as Resizable<PERSON><PERSON><PERSON>,
  Panel as Resizable<PERSON>ane<PERSON>,
  PanelGroup as ResizablePanelGroup,
} from "react-resizable-panels";

interface ChatPageProps {
  params: Promise<{ slug: string }>;
}

function ChatHeader({
  room,
  onlineMembers,
  onBackClick,
  showBackButton = false,
  openContractDialog,
}: {
  room: any;
  onlineMembers: number;
  onBackClick?: () => void;
  showBackButton?: boolean;
  openContractDialog: (open: boolean) => void;
}) {
  if (!room) {
    return (
      <div className="h-28 border-b bg-white flex items-center justify-center">
        <div className="text-gray-500">
          Select a conversation to start chatting
        </div>
      </div>
    );
  }

  return (
    <div className="h-20 border-b border-gray-200 flex items-center justify-between px-4">
      <div className="flex items-center gap-3">
        {showBackButton && (
          <Button
            variant="ghost"
            size="sm"
            className="h-8 w-8 p-0 md:hidden"
            onClick={onBackClick}
          >
            <svg
              className="h-4 w-4"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M15 19l-7-7 7-7"
              />
            </svg>
          </Button>
        )}
        <div className="h-10 w-10 rounded-lg bg-gray-100 flex items-center justify-center">
          <Hash className="h-5 w-5 text-gray-600" />
        </div>
        <div className="flex flex-col">
          <p className="text-lg font-semibold">{room.name}</p>
          <div className="flex items-center gap-2 text-sm text-gray-500">
            <Users className="h-3 w-3" />
            <span>{room.members?.length || 0} members</span>
            {onlineMembers > 0 && (
              <>
                <span>•</span>
                <div className="flex items-center gap-1">
                  <div className="h-2 w-2 bg-green-500 rounded-full"></div>
                  <span>{onlineMembers} online</span>
                </div>
              </>
            )}
          </div>
        </div>
      </div>

      <div className="flex items-center gap-2">
        <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
          <Phone className="h-4 w-4" />
        </Button>
        <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
          <Video className="h-4 w-4" />
        </Button>
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
              <MoreVertical className="h-4 w-4" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end" className="w-48">
            <DropdownMenuItem
              onClick={() => openContractDialog(true)}
              className="gap-2"
            >
              <FileText className="h-4 w-4" />
              View Contract
            </DropdownMenuItem>
            <DropdownMenuSeparator />
            <DropdownMenuItem
              onClick={() => {
                // TODO: Implement leave room functionality
                console.log("Leave room:", room?.id);
              }}
              className="gap-2 text-red-600 focus:text-red-600"
            >
              <LogOut className="h-4 w-4" />
              Leave Room
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </div>
    </div>
  );
}

function EmptyState() {
  return (
    <div className="flex-1 flex items-center justify-center ">
      <div className="text-center max-w-md">
        <div className="h-16 w-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
          <MessageCircle className="h-8 w-8 text-blue-600" />
        </div>
        <h3 className="text-lg font-semibold text-gray-900 mb-2">
          Welcome to Chat
        </h3>
        <p className="text-gray-500 mb-4">
          Select a conversation from the sidebar to start chatting with your
          team members.
        </p>
        <div className="text-sm text-gray-400">
          💡 Tip: Use rich text formatting, share files, and collaborate in
          real-time
        </div>
      </div>
    </div>
  );
}

export default function ChatPage({ params: _ }: ChatPageProps) {
  const {
    rooms,
    currentRoom,
    currentRoomId,
    messages,
    isLoading,
    isLoadingMessages,
    isSendingMessage,
    sendMessage,
    selectRoom,
    startTyping,
    stopTyping,
    replyToMessage,
    closeReply,
    updateMessage,
    copyMessage,
    deleteMessage,
    updateRoom,
    deleteRoom,
    currentUser,
    toReply,
  } = useChat();

  const [showCreateRoom, setShowCreateRoom] = useState(false);
  const [sidebarSize, setSidebarSize] = useState(25);
  const [isMobile, setIsMobile] = useState(false);
  const [isResizing, setIsResizing] = useState(false);
  const panelGroupRef = useRef<any>(null);
  const resizeTimeoutRef = useRef<ReturnType<typeof setTimeout> | undefined>(
    undefined
  );
  const [viewContractDialog, setViewContractDialogOpen] = useState(false);
  const [editRoomDialogOpen, setEditRoomDialogOpen] = useState(false);
  const [selectedRoom, setSelectedRoom] = useState<any>(null);

  // Save sidebar size to localStorage and detect mobile
  useEffect(() => {
    const savedSize = localStorage.getItem("chat-sidebar-size");
    if (savedSize) {
      setSidebarSize(parseInt(savedSize, 30));
    }

    // Mobile detection
    const checkMobile = () => {
      setIsMobile(window.innerWidth < 768);
    };

    checkMobile();
    window.addEventListener("resize", checkMobile);

    return () => window.removeEventListener("resize", checkMobile);
  }, []);

  const handleSidebarResize = (size: number) => {
    setSidebarSize(size);
    localStorage.setItem("chat-sidebar-size", size.toString());
  };

  // Reset to default size
  const resetPanelSizes = () => {
    const defaultSize = 25;
    setSidebarSize(defaultSize);
    localStorage.setItem("chat-sidebar-size", defaultSize.toString());

    // Programmatically resize the panels
    if (panelGroupRef.current) {
      panelGroupRef.current.setLayout([defaultSize, 100 - defaultSize]);
    }
  };

  // Calculate online members for current room
  const onlineMembers =
    currentRoom?.members?.filter(
      (m: Record<string, any>) => m.state === "online"
    ).length || 0;

  // Handle room selection
  const handleSelectRoom = (roomId: string) => {
    selectRoom(roomId);
  };

  const handleEditRoom = (room: any) => {
    setEditRoomDialogOpen(true);
    setSelectedRoom(room);
  };

  const handleDeleteRoom = (room: any) => {
    deleteRoom(room.id);
  };

  // Handle send message
  const handleSendMessage = (
    content: string,
    associations?: any[],
    attachments?: any[]
  ) => {
    if (currentRoomId) {
      sendMessage(content, associations, attachments);
    }
  };

  // Handle typing indicators
  const handleStartTyping = () => {
    if (currentRoomId) {
      startTyping(currentRoomId);
    }
  };

  const handleStopTyping = () => {
    if (currentRoomId) {
      stopTyping(currentRoomId);
    }
  };

  // Handle back navigation on mobile
  const handleBackToRooms = () => {
    selectRoom(null);
  };

  // Keyboard shortcuts
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      // Escape key to go back to rooms on mobile
      if (event.key === "Escape" && isMobile && currentRoom) {
        handleBackToRooms();
      }
    };

    window.addEventListener("keydown", handleKeyDown);
    return () => window.removeEventListener("keydown", handleKeyDown);
  }, [isMobile, currentRoom, handleBackToRooms]);

  // Cleanup resize timeout on unmount
  useEffect(() => {
    return () => {
      if (resizeTimeoutRef.current) {
        clearTimeout(resizeTimeoutRef.current);
      }
    };
  }, []);

  return (
    <div className="h-screen">
      <ResizablePanelGroup
        ref={panelGroupRef}
        direction="horizontal"
        className={cn("h-full", isResizing && "select-none")}
        onLayout={(sizes: number[]) => {
          if (sizes[0] && Math.abs(sizes[0] - sidebarSize) > 0.1) {
            // Set resizing state
            setIsResizing(true);

            // Clear existing timeout
            if (resizeTimeoutRef.current) {
              clearTimeout(resizeTimeoutRef.current);
            }

            // Update size
            handleSidebarResize(sizes[0]);

            // Stop resizing after a delay
            resizeTimeoutRef.current = setTimeout(() => {
              setIsResizing(false);
            }, 150);
          }
        }}
      >
        {/* Left sidebar - Room list */}
        <ResizablePanel
          defaultSize={isMobile ? 100 : sidebarSize}
          minSize={isMobile ? 100 : 30}
          maxSize={isMobile ? 100 : 40}
          className={cn(
            isMobile ? "min-w-full" : "min-w-[280px]",
            currentRoom && isMobile ? "hidden" : ""
          )}
        >
          <RoomList
            rooms={rooms}
            currentRoomId={currentRoomId}
            onSelectRoom={handleSelectRoom}
            onCreateRoom={() => setShowCreateRoom(true)}
            onEditRoom={handleEditRoom}
            onDeleteRoom={handleDeleteRoom}
            isLoading={isLoading}
            className="h-full"
          />
        </ResizablePanel>

        <ResizableHandle
          className="w-[1px] bg-zinc-900 hover:bg-zinc-400 active:bg-zinc-500 transition-all duration-200 relative group cursor-col-resize"
          onDoubleClick={resetPanelSizes}
        >
          <div className="absolute inset-y-0 left-1/2 transform -translate-x-1/2 w-[1px] bg-zinc-800 group-hover:bg-zinc-400 group-active:bg-zinc-500 transition-colors duration-200" />

          {/* Tooltip on hover */}
          <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 bg-zinc-800 text-white text-xs px-2 py-1 rounded opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none whitespace-nowrap">
            Double-click to reset
          </div>
          {/* Visual indicator when resizing */}
          {isResizing && (
            <>
              <div className="absolute inset-y-0 left-1/2 transform -translate-x-1/2 w-1 bg-zinc-500 shadow-lg" />
              <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 bg-blue-600 text-white text-xs px-2 py-1 rounded shadow-lg pointer-events-none">
                {Math.round(sidebarSize)}%
              </div>
            </>
          )}
        </ResizableHandle>

        {/* Right side - Chat area */}
        <ResizablePanel
          defaultSize={isMobile ? 100 : 100 - sidebarSize}
          minSize={isMobile ? 100 : 60}
          className={cn(!currentRoom && isMobile ? "hidden" : "")}
        >
          <div className="flex flex-col h-full">
            {currentRoom ? (
              <>
                {/* Chat header */}
                <ChatHeader
                  room={currentRoom}
                  onlineMembers={onlineMembers}
                  onBackClick={handleBackToRooms}
                  showBackButton={isMobile}
                  openContractDialog={setViewContractDialogOpen}
                />

                {/* Messages area */}
                <MessageList
                  messages={messages}
                  replyToMessage={replyToMessage}
                  updateMessage={updateMessage}
                  copyMessage={copyMessage}
                  deleteMessage={deleteMessage}
                  //
                  currentUserId={currentUser?.id}
                  isLoading={isLoadingMessages}
                  className="flex-1"
                />

                {/* Message composer */}
                <MessageComposer
                  toReply={toReply}
                  closeReply={closeReply}
                  onSendMessage={handleSendMessage}
                  onStartTyping={handleStartTyping}
                  onStopTyping={handleStopTyping}
                  disabled={isSendingMessage}
                  placeholder={`Message ${currentRoom.name}...`}
                />
              </>
            ) : (
              <EmptyState />
            )}
          </div>
        </ResizablePanel>
      </ResizablePanelGroup>
      {/*  */}
      <ContractDetailsDialog
        contractId={currentRoom?.contractId}
        isOpen={viewContractDialog}
        onOpenChange={() => {
          setViewContractDialogOpen(false);
        }}
      />

      <CreateRoomDialog
        open={showCreateRoom}
        onOpenChange={setShowCreateRoom}
      />

      <EditRoomDialog
        open={editRoomDialogOpen}
        room={selectedRoom}
        onOpenChange={() => setEditRoomDialogOpen(false)}
      />
    </div>
  );
}
