"use client";

import React, { Suspense } from "react";
import { ContractDetailsContainer } from "@/components/view/contracts/details/container";

interface ContractDetailsPageProps
  extends Promise<{ id: string; slug: string }> {
  id: string;
  slug: string;
}

export default async function ContractDetailsPage({
  params,
}: {
  params: Promise<ContractDetailsPageProps>;
}) {
  let param: any = await params;

  return (
    <Suspense fallback={<div>Loading contract details...</div>}>
      <ContractDetailsContainer contractId={param.id} />
    </Suspense>
  );
}
