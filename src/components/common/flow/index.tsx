"use client";

import React, { use<PERSON><PERSON>back, useMemo, useState } from "react";
import { cn } from "@/lib/utils";
import {
  ReactFlow,
  Node,
  addEdge,
  Connection,
  useNodesState,
  useEdgesState,
  Controls,
  Background,
  BackgroundVariant,
  NodeTypes,
  Handle,
  Position,
} from "@xyflow/react";
import "@xyflow/react/dist/style.css";
import { Advanced } from "@/layouts/dashboard/advanced";
import type { DataType } from "@/layouts/dashboard/advanced";
import {
  ContextMenu,
  ContextMenuContent,
  ContextMenuItem,
  ContextMenuSeparator,
  ContextMenuTrigger,
} from "@/components/common/ui/context-menu";

// Custom node component for files/folders
const FileNode = ({ data }: { data: any }) => {
  const { item, getDataType, onItemClick, contextMenuActions } = data;
  const dataType = getDataType ? getDataType(item) : "default";
  const name = item.name || item.title || "Untitled";

  const nodeContent = (
    <div
      className="px-4 py-3 bg-black border-2 border-gray-200 rounded-lg shadow-sm hover:shadow-md transition-shadow cursor-pointer min-w-[120px]"
      onClick={() => onItemClick?.(item)}
    >
      {/* Thumbnail or Icon */}
      <div className="flex justify-center items-start h-12 w-full mb-2">
        {item?.path ? (
          <object
            className="w-full h-full rounded"
            data={item.thumbnail}
            type={item.mime_type}
          >
            {Advanced.getIconForDataType(dataType, 32)}
          </object>
        ) : (
          Advanced.getIconForDataType(dataType, 32)
        )}
      </div>

      {/* File name */}
      <div className="text-center">
        <span className="text-xs font-medium text-gray-700 truncate block">
          {name}
        </span>
        {item.size && (
          <span className="text-xs text-gray-500 block mt-1">
            {formatFileSize(item.size)}
          </span>
        )}
      </div>

      {/* Connection handles */}
      <Handle type="target" position={Position.Top} className="opacity-0" />
      <Handle type="source" position={Position.Bottom} className="opacity-0" />
    </div>
  );

  // If no context menu actions, return node without context menu
  if (!contextMenuActions || contextMenuActions.length === 0) {
    return nodeContent;
  }

  return (
    <ContextMenu>
      <ContextMenuTrigger asChild>{nodeContent}</ContextMenuTrigger>
      <ContextMenuContent className="w-48">
        {contextMenuActions.map((action: ContextMenuAction, index: number) => (
          <React.Fragment key={action.id}>
            {action.separator && index > 0 && <ContextMenuSeparator />}
            <ContextMenuItem
              onClick={() => action.onClick(item)}
              disabled={action.disabled}
              className="flex items-center gap-2"
            >
              {action.icon}
              <span>{action.label}</span>
            </ContextMenuItem>
          </React.Fragment>
        ))}
      </ContextMenuContent>
    </ContextMenu>
  );
};

// Folder node component
const FolderNode = ({ data }: { data: any }) => {
  const { item, onItemClick, contextMenuActions } = data;
  const name = item.name || item.title || "Untitled Folder";

  const nodeContent = (
    <div
      className="px-4 py-3 bg-blue-50 border-2 border-blue-200 rounded-lg shadow-sm hover:shadow-md transition-shadow cursor-pointer min-w-[120px]"
      onClick={() => onItemClick?.(item)}
    >
      {/* Folder icon */}
      <div className="flex justify-center items-center h-12 w-full mb-2">
        {Advanced.getIconForDataType("folder", 32)}
      </div>

      {/* Folder name */}
      <div className="text-center">
        <span className="text-xs font-medium text-blue-700 truncate block">
          {name}
        </span>
        {item.itemCount && (
          <span className="text-xs text-blue-500 block mt-1">
            {item.itemCount} items
          </span>
        )}
      </div>

      {/* Connection handles */}
      <Handle type="target" position={Position.Top} className="opacity-0" />
      <Handle type="source" position={Position.Bottom} className="opacity-0" />
    </div>
  );

  // If no context menu actions, return node without context menu
  if (!contextMenuActions || contextMenuActions.length === 0) {
    return nodeContent;
  }

  return (
    <ContextMenu>
      <ContextMenuTrigger asChild>{nodeContent}</ContextMenuTrigger>
      <ContextMenuContent className="w-48">
        {contextMenuActions.map((action: ContextMenuAction, index: number) => (
          <React.Fragment key={action.id}>
            {action.separator && index > 0 && <ContextMenuSeparator />}
            <ContextMenuItem
              onClick={() => action.onClick(item)}
              disabled={action.disabled}
              className="flex items-center gap-2"
            >
              {action.icon}
              <span>{action.label}</span>
            </ContextMenuItem>
          </React.Fragment>
        ))}
      </ContextMenuContent>
    </ContextMenu>
  );
};

// Node types
const nodeTypes: NodeTypes = {
  file: FileNode,
  folder: FolderNode,
};

// Context menu action interface
export interface ContextMenuAction {
  id: string;
  label: string;
  icon?: React.ReactNode;
  onClick: (item: any) => void;
  disabled?: boolean;
  separator?: boolean;
}

// Utility function to format file size
const formatFileSize = (size: string | number): string => {
  const bytes = typeof size === "string" ? parseInt(size) : size;
  if (bytes === 0) return "0 B";
  const k = 1024;
  const sizes = ["B", "KB", "MB", "GB"];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
};

// Props interface
export interface FlowGridProps {
  data?: any[];
  loading?: boolean;
  emptyState?: React.ReactNode;
  onItemClick?: (item: any) => void;
  className?: string;
  getDataType?: (item: any) => DataType;
  contextMenuActions?: ContextMenuAction[];
}

// Main Flow Grid component
export function FlowGrid({
  data = [],
  loading = false,
  emptyState,
  onItemClick,
  className = "",
  getDataType,
  contextMenuActions = [],
}: FlowGridProps) {
  // Convert data to nodes
  const initialNodes: Node[] = useMemo(() => {
    return data.map((item, index) => {
      const isFolder = item.type === "folder" || item.folder;
      const row = Math.floor(index / 6); // 6 items per row
      const col = index % 6;

      return {
        id: item.id || `item-${index}`,
        type: isFolder ? "folder" : "file",
        position: {
          x: col * 150 + 50, // 150px spacing between nodes
          y: row * 120 + 50, // 120px vertical spacing
        },
        data: {
          item,
          getDataType,
          onItemClick,
          contextMenuActions,
        },
        draggable: true,
      };
    });
  }, [data, getDataType, onItemClick, contextMenuActions]);

  const [nodes, setNodes, onNodesChange] = useNodesState(initialNodes);
  const [edges, setEdges, onEdgesChange] = useEdgesState([]);

  // Handle connections (for future folder hierarchy)
  const onConnect = useCallback(
    (params: Connection) => setEdges((eds) => addEdge(params, eds)),
    [setEdges]
  );

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-muted-foreground">Loading...</div>
      </div>
    );
  }

  if (!data.length && emptyState) {
    return <div className="h-64">{emptyState}</div>;
  }

  return (
    <div className={cn("w-full h-screen", className)}>
      <ReactFlow
        nodes={nodes}
        edges={edges}
        onNodesChange={onNodesChange}
        onEdgesChange={onEdgesChange}
        onConnect={onConnect}
        nodeTypes={nodeTypes}
        fitView
        fitViewOptions={{ padding: 0.2 }}
      >
        <Controls />
        <Background variant={BackgroundVariant.Dots} gap={20} size={1} />
      </ReactFlow>
    </div>
  );
}
