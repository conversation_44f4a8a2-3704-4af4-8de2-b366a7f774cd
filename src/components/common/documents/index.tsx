"use client";

import React, { useState, useEffect } from "react";

import {
  Combobox,
  ComboboxInput,
  ComboboxTrigger,
  ComboboxContent,
  ComboboxCommand,
  ComboboxList,
  ComboboxEmpty,
  ComboboxGroup,
  ComboboxItem,
  ComboboxItemIndicator,
} from "../ui/combobox";
import { Badge } from "@/components/common/ui/badge";
import { Building2, Calendar, DollarSign } from "lucide-react";
import { toast } from "sonner";
import { Upload } from "lucide-react";
import { useDocuments } from "@/hooks/useDocuments";

export const useDocumentHandler = () => {
  let { selectedFiles, setSelectedFiles, Uploader } = useUploader();
  let { selectedLibrary, setSelectedLibrary, LibrarySelector } =
    useLibrarySelector();

  const DocumentHandler = ({
    setSelectedLibrary,
    setSelectedFiles,
  }: {
    setSelectedLibrary: any;
    setSelectedFiles: any;
  }) => {
    return (
      <div className="w-full h-full flex flex-col gap-3">
        <LibrarySelector
          selectedLibrary={selectedLibrary}
          setSelectedLibrary={setSelectedLibrary}
          disabled={false}
          placeholder="Select a document to attach.."
        />
        <Uploader
          selectedFiles={selectedFiles}
          setSelectedFiles={setSelectedFiles}
        />
      </div>
    );
  };

  return {
    selectedFiles,
    selectedLibrary,
    setSelectedFiles,
    setSelectedLibrary,
    DocumentHandler,
  };
};

const useLibrarySelector = () => {
  const [selectedLibrary, setSelectedLibrary] = useState("google");
  return {
    selectedLibrary,
    setSelectedLibrary,
    LibrarySelector,
  };
};

interface LibrarySelectorProps {
  selectedLibrary?: string;
  setSelectedLibrary: (value: any) => any;
  placeholder?: string;
  disabled?: boolean;
}

function LibrarySelector({
  selectedLibrary,
  setSelectedLibrary,
  disabled = false,
  placeholder = "Select a document...",
}: LibrarySelectorProps) {
  const { documents, isLoading, error } = useDocuments();
  const [open, setOpen] = useState(false);
  const [searchValue, setSearchValue] = useState("");

  // Find selected document
  const selectedDoc: any = documents.find(
    (document: any) => document.id === selectedLibrary
  );

  // Filter documents based on search
  const filteredanys = documents.filter(
    (document: any) =>
      document.name.toLowerCase().includes(searchValue.toLowerCase()) ||
      document.category.toLowerCase().includes(searchValue.toLowerCase()) ||
      document.status.toLowerCase().includes(searchValue.toLowerCase())
  );

  // Group documents by status
  const groupedanys = filteredanys.reduce(
    (acc: Record<string, any[]>, document: any) => {
      const status = document.status;
      if (!acc[status]) {
        acc[status] = [];
      }
      acc[status].push(document);
      return acc;
    },
    {}
  );

  const handleSelect = (documentId: string) => {
    if (documentId === selectedLibrary) {
      setSelectedLibrary(undefined); // Deselect if same document is clicked
    } else {
      setSelectedLibrary(documentId);
    }
    setOpen(false);
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case "active":
        return "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300";
      case "draft":
        return "bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300";
      case "completed":
        return "bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300";
      case "terminated":
        return "bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300";
      case "expired":
        return "bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-300";
      default:
        return "bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300";
    }
  };

  if (error) {
    return (
      <div className="text-sm text-red-600 dark:text-red-400">
        Failed to load documents
      </div>
    );
  }

  return (
    <Combobox open={open} onOpenChange={setOpen}>
      <ComboboxTrigger
        disabled={disabled || isLoading}
        placeholder={isLoading ? "Loading documents..." : placeholder}
      >
        {selectedDoc ? (
          <div className="flex items-center justify-between w-full">
            <div className="flex items-center space-x-2">
              <Building2 className="h-4 w-4 text-muted-foreground" />
              <span className="truncate">{selectedDoc.name}</span>
            </div>
            <Badge
              variant="secondary"
              className={`text-xs ${getStatusColor(selectedDoc.status)}`}
            >
              {selectedDoc.status}
            </Badge>
          </div>
        ) : null}
      </ComboboxTrigger>

      <ComboboxContent className="w-[400px]">
        <ComboboxCommand>
          <ComboboxInput
            placeholder="Search documents..."
            value={searchValue}
            onValueChange={setSearchValue}
          />
          <ComboboxList>
            <ComboboxEmpty>
              {isLoading ? "Loading documents..." : "No documents found."}
            </ComboboxEmpty>

            {Object.entries(groupedanys).map(([status, statusanys]) => (
              <ComboboxGroup
                key={status}
                heading={status.charAt(0).toUpperCase() + status.slice(1)}
              >
                {(statusanys as any[]).map((document: any) => (
                  <ComboboxItem
                    key={document.id}
                    value={document.id}
                    onSelect={() => handleSelect(document.id)}
                    className="flex flex-col items-start space-y-1 p-3"
                  >
                    <div className="flex items-center justify-between w-full">
                      <div className="flex items-center space-x-2">
                        <Building2 className="h-4 w-4 text-muted-foreground" />
                        <span className="font-medium">{document.name}</span>
                      </div>
                      <ComboboxItemIndicator
                        isSelected={document.id === selectedLibrary}
                      />
                    </div>
                  </ComboboxItem>
                ))}
              </ComboboxGroup>
            ))}
          </ComboboxList>
        </ComboboxCommand>
      </ComboboxContent>
    </Combobox>
  );
}

const useUploader = () => {
  const [selectedFiles, setSelectedFiles] = useState<any[]>([]);
  return { selectedFiles, setSelectedFiles, Uploader };
};

const Uploader = ({
  selectedFiles,
  setSelectedFiles,
}: {
  selectedFiles: File[];
  setSelectedFiles: (files: File[]) => void;
}) => {
  function handleSelectedFiles(e: React.ChangeEvent<HTMLInputElement>) {
    let { files }: { files: any } = e.target ?? { files: [] };
    if (!files) toast.error("No files selected");
    setSelectedFiles(Array.from(files));
  }

  return (
    <div className="border-2 border-dashed border-gray-300 dark:border-gray-600 rounded-lg p-4">
      <input
        id="documents"
        type="file"
        multiple
        onChange={handleSelectedFiles}
        className="hidden"
        accept=".pdf,.doc,.docx,.txt"
      />
      <label
        htmlFor="documents"
        className="cursor-pointer flex flex-col items-center justify-center space-y-2"
      >
        <Upload className="h-8 w-8 text-gray-400" />
        <span className="text-sm text-gray-500">
          Click to upload document documents
        </span>
        <span className="text-xs text-gray-400">
          PDF, DOC, DOCX, TXT files supported
        </span>
      </label>
    </div>
  );
};
