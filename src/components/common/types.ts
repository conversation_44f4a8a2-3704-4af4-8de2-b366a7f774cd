// Art Component Types
export interface GridArtProps {
  amount?: number;
}

// CTA Component Types
export interface CallToActionsProps {
  theme?: "dark" | "light";
}

// Footer Component Types
export interface FooterProps {
  className?: string;
}

export interface ContactItem {
  title: string;
  email: string;
  url: string;
}

// Tab Component Types
export interface TabStyle {
  width?: string;
  theme?:
    | "primary"
    | "secondary"
    | "muted"
    | "accent"
    | "outline"
    | "lime"
    | "navy"
    | "gray"
    | "black";
}

export interface ListTabProps {
  title?: string;
  caption?: string;
  icon?: string;
  style?: TabStyle;
  position?: "start" | "center" | "end";
}

export interface RowTabProps {
  title?: string;
  caption?: string;
  style?:
    | "primary"
    | "secondary"
    | "muted"
    | "accent"
    | "lime"
    | "blue"
    | "navy"
    | "gray"
    | "black";
}

// Loader Component Types
export interface LoaderProps {
  active: boolean;
}

// Scroll Component Types
export interface ScrollAnimationProps {
  className?: string;
}

// Logo Component Types
export interface BrandProps {
  size?: "lg" | "md" | "sm";
  style?: string;
}

// Heading Component Types
export interface HeadingProps {
  tagline?: string;
  title?: string;
  className?: string;
}
