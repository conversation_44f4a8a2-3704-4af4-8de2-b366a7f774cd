"use client";

import { cn } from "@/lib/utils";
import { usePreference } from "@/hooks/usePreference";
import { Card, CardContent } from "@/components/common/ui/card";

import { MoonStar, SunMedium } from "lucide-react";
import { RiLayout5Fill as BasicLayoutIcon } from "react-icons/ri";
import { RiLayoutMasonryFill as AdvancedLayoutIcon } from "react-icons/ri";

export const PreferenceTab = () => {
  return (
    <div className="space-y-8">
      <Section
        about={{
          title: "User Interface Mode",
          description: "Choose your view mode",
        }}
      >
        <SettingTab
          id="layout"
          value="basic"
          title="Basic Layout"
          description="Default paginated view"
          icon={BasicLayoutIcon}
        />
        <SettingTab
          id="layout"
          value="advanced"
          title="Advanced Layout"
          description="Scrolling view"
          icon={AdvancedLayoutIcon}
        />
      </Section>
      <Section
        about={{
          title: "Theme",
          description: "Choose your default theme",
        }}
        className="flex flex-row gap-6"
      >
        <SettingTab
          id="theme"
          value="light"
          title="Light"
          description=""
          icon={SunMedium}
        />
        <SettingTab
          id="theme"
          value="dark"
          title="Dark"
          description=""
          icon={MoonStar}
        />
      </Section>
    </div>
  );
};

const Section = ({
  about,
  className,
  children,
}: {
  about: { title: string; description: string };
  className?: string;
  children: React.ReactNode;
}) => {
  return (
    <div className="space-y-6">
      <div className="space-y-1">
        <h2 className="text-lg font-semibold">{about.title}</h2>
        <p className="text-sm text-gray-600">{about.description}</p>
      </div>
      <div className={cn("space-y-6", className)}>{children}</div>
    </div>
  );
};

const SettingTab = ({
  id,
  value,
  title,
  description,
  icon: Icon,
}: SettingTabProps) => {
  const { settings, setPreferenceSettings } = usePreference();
  //
  return (
    <Card
      className={cn("w-full h-full", {
        "bg-primary/5": settings[id as keyof typeof settings] === value,
      })}
      onClick={() => {
        console.log("Id:" + id + " Value:" + value);
        if (id) {
          setPreferenceSettings({
            ...settings,
            [id]: value,
          });
        }
      }}
    >
      <CardContent className="flex flex-row items-start justify-between gap-4">
        <div className="flex flex-col gap-1">
          <h3 className="text-sm font-medium">{title}</h3>
          <p className="text-sm text-gray-600">{description}</p>
        </div>
        <Icon size={48} />
      </CardContent>
    </Card>
  );
};

interface SettingTabProps {
  value: string;
  id: string;
  title: string;
  description: string;
  icon: React.ComponentType;
}
