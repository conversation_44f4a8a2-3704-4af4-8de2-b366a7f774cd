"use client";

import React, { useEffect, useState } from "react";
import {
  <PERSON>,
  Card<PERSON>ontent,
  CardHeader,
  CardTitle,
} from "@/components/common/ui/card";
import { Badge } from "@/components/common/ui/badge";
import { Separator } from "@/components/common/ui/separator";
import {
  FileText,
  Link as LinkIcon,
  Target,
  DollarSign,
  CheckCircle2,
  Clock,
} from "lucide-react";
import { format } from "date-fns";
import { DatabaseCrypto } from "@/lib/crypto/middleware";
import ReactHtmlParser from "react-html-parser";
import { type ProposalFormData } from "@/components/view/proposals/create-dialog";
import type { Proposal } from "@/lib/api/validators/schemas/proposal";

interface ProposalDetailsContentProps {
  proposal: Proposal;
}

export function ProposalDetailsContent({
  proposal,
}: ProposalDetailsContentProps) {
  const [formData, setFormData] = useState<ProposalFormData | null>(null);

  const formatCurrency = (amount: number) => {
    if (!amount) return "$0";
    return new Intl.NumberFormat("en-US", {
      style: "currency",
      currency: "USD",
    }).format(amount);
  };

  const formatDate = (date: string | Date) => {
    if (!date) return "";
    return format(new Date(date), "PPP");
  };

  async function decryptProposalDescription() {
    // Decrypt the description
    const convertedData: ProposalFormData = {
      id: proposal.id || "",
      createdAt: proposal.createdAt,
      updatedAt: proposal.updatedAt,
      name: proposal.name || "",
      description: proposal.description || "",
      budgetType: proposal?.milestones?.length > 0 ? "milestone" : "fixed", // Default to fixed, could be enhanced to detect from data
      fixedBudget: proposal.fixed_budget || proposal.total_budget || 0,
      totalBudget: proposal.total_budget || 0,
      milestones: proposal.milestones || [],
      duration: proposal.duration || 1,
      agreedToTerms: proposal.agreed_to_terms_and_conditions || false,
      links: proposal.links || [],
    };

    try {
      let decryptedProposal = await DatabaseCrypto.decryptFields(
        convertedData,
        ["description"]
      );

      setFormData(decryptedProposal);
    } catch (error: any) {
      console.error("Failed to decrypt proposal description:", error);
    }
  }

  useEffect(() => {
    decryptProposalDescription();
  }, [proposal]);

  if (!formData) {
    return null;
  }

  return (
    <div className="grid grid-cols-1 lg:grid-cols-1 gap-8">
      {/* Left Column */}
      <div className="space-y-6">
        {/* Description */}
        {formData.description && (
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <FileText className="h-5 w-5" />
                Description
              </CardTitle>
            </CardHeader>
            <CardContent>
              <span className="text-muted-foreground leading-relaxed whitespace-pre-wrap">
                {ReactHtmlParser(formData.description)}
              </span>
            </CardContent>
          </Card>
        )}

        {/* Budget Details */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <DollarSign className="h-5 w-5" />
              Budget Information
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div className="flex flex-col gap-2">
                <p className="text-sm text-muted-foreground">Total Budget</p>
                <p className="text-2xl font-bold text-green-600">
                  {formatCurrency(formData.totalBudget)}
                </p>
              </div>
              {formData.fixedBudget > 0 && (
                <div className="flex flex-col gap-2">
                  <p className="text-sm text-muted-foreground">Fixed Budget</p>
                  <p className="text-2xl font-bold">
                    {formatCurrency(formData.fixedBudget)}
                  </p>
                </div>
              )}
            </div>

            <Separator />

            <div className="flex items-center justify-between">
              <span className="text-sm text-muted-foreground">Budget Type</span>
              <Badge variant="outline">
                {formData.fixedBudget > 0 ? "Fixed Budget" : "Milestone-based"}
              </Badge>
            </div>
          </CardContent>
        </Card>

        {/* Milestones */}
        {formData.milestones && formData.milestones.length > 0 && (
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Target className="h-5 w-5" />
                Milestones
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {formData.milestones.map(
                  (milestone: unknown, index: number) => (
                    <div
                      key={index}
                      className="w-full flex items-center gap-3 p-3 bg-muted/50 rounded-lg"
                    >
                      <div className="flex-shrink-0 w-6 h-6 bg-primary text-primary-foreground rounded-full flex items-center justify-center text-xs font-medium">
                        {index + 1}
                      </div>
                      <div className="w-full flex flex-row justify-between items-center">
                        <p className="font-medium">
                          {(milestone as any)?.description ||
                            `Milestone ${index + 1}`}
                        </p>
                        {(milestone as any)?.amount && (
                          <p className="text-sm text-lime-250 font-medium">
                            Amount: {formatCurrency((milestone as any).amount)}
                          </p>
                        )}
                        {(milestone as any)?.dueDate && (
                          <p className="text-sm text-lime-250">
                            Due: {formatDate((milestone as any).dueDate)}
                          </p>
                        )}
                      </div>
                    </div>
                  )
                )}
              </div>
            </CardContent>
          </Card>
        )}
      </div>

      {/* Right Column */}
      <div className="space-y-6">
        {/* Project Details */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Clock className="h-5 w-5" />
              Project Details
            </CardTitle>
          </CardHeader>
          {/*  */}
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 gap-3">
              <div className="flex justify-between items-center">
                <span className="text-sm text-muted-foreground">Duration</span>
                <span className="font-medium">{formData.duration} days</span>
              </div>

              <div className="flex justify-between items-center">
                <span className="text-sm text-muted-foreground">Created</span>
                <span className="font-medium">
                  {formatDate(formData.createdAt)}
                </span>
              </div>

              <div className="flex justify-between items-center">
                <span className="text-sm text-muted-foreground">
                  Last Updated
                </span>
                <span className="font-medium">
                  {formatDate(formData.updatedAt)}
                </span>
              </div>

              <Separator />

              <div className="flex justify-between items-center">
                <span className="text-sm text-muted-foreground">
                  Terms & Conditions
                </span>
                <div className="flex items-center gap-2">
                  {formData.agreedToTerms ? (
                    <>
                      <CheckCircle2 className="h-4 w-4 text-green-500" />
                      <span className="text-green-600 font-medium">Agreed</span>
                    </>
                  ) : (
                    <>
                      <Clock className="h-4 w-4 text-orange-500" />
                      <span className="text-orange-600 font-medium">
                        Pending
                      </span>
                    </>
                  )}
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Links */}
        {formData.links && formData.links.length > 0 && (
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <LinkIcon className="h-5 w-5" />
                Related Links
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                {formData.links.map((link: string, index: number) => (
                  <a
                    key={index}
                    href={link}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="flex items-center gap-2 p-2 rounded-lg hover:bg-muted/50 transition-colors"
                  >
                    <LinkIcon className="h-4 w-4 text-muted-foreground" />
                    <span className="text-sm text-blue-600 hover:underline truncate">
                      {link}
                    </span>
                  </a>
                ))}
              </div>
            </CardContent>
          </Card>
        )}

        {/* Attachments section removed - not part of Prisma schema */}
      </div>
    </div>
  );
}
