"use client";

import { useState, useEffect } from "react";
import { useParams } from "next/navigation";
import { useAuth } from "@/hooks/useAuth";
import { useProposal } from "@/hooks/useProposal";
import { CanRead } from "@/components/common/rbac/RBACWrapper";
import { DEFAULT_ENTITIES } from "@/lib/rbac";
import { Listing } from "@/layouts/dashboard/basic";
import { type Proposal } from "@/data/proposals-mock";
import type { Proposal as ApiProposal } from "@/lib/api/validators/schemas/proposal";
import { ProposalHeader } from "./header";
import { ProposalStatistics } from "./statistics";
import { ProposalTable } from "./table";
import { ProposalForm, type ProposalFormData } from "./create-dialog";

// Type adapter to convert API proposals to UI proposals
const adaptApiProposalToUI = (apiProposal: ApiProposal): Proposal => {
  // Map API status to UI status
  const statusMap: Record<string, Proposal["status"]> = {
    created: "draft",
    submitted: "pending",
    received: "pending",
    negotiating: "pending",
    agreed: "approved",
    inprogress: "approved",
    reviewing: "pending",
    completed: "completed",
  };

  return {
    id: apiProposal.id,
    name: apiProposal.name,
    client: apiProposal.client?.name || "",
    description: apiProposal.description || "",
    status: statusMap[apiProposal.status] || "draft",
    budgetType: apiProposal.fixed_budget > 0 ? "fixed" : "milestone",
    fixedBudget: apiProposal.fixed_budget,
    totalBudget: apiProposal.total_budget,
    duration: apiProposal.duration,
    attachments: apiProposal.links || [],
    createdDate: new Date(apiProposal.createdAt).toISOString(),
    lastModified: new Date(apiProposal.updatedAt).toISOString(),
    milestones: apiProposal.milestones || [],
    agreedToTerms: apiProposal.agreed_to_terms_and_conditions,
  };
};

// Main Container Component
function ProposalsContainer() {
  const { slug } = useParams();
  const { personalizedRoute } = useAuth();

  const {
    proposals,
    statistics,
    isLoading,
    error,
    create,
    update,
    remove,
    clearError,
  } = useProposal();

  const [showForm, setShowForm] = useState(false);

  // No initialization needed - SWR handles data fetching automatically

  useEffect(() => {
    if (error) {
      console.error("Proposal error:", error);
    }
  }, [error]);

  useEffect(() => {
    return () => {
      clearError();
    };
  }, [clearError]);

  const handleCreateProposal = async (formData: any) => {
    try {
      await create(formData);
      setShowForm(false);
    } catch (error) {
      console.error("Failed to create proposal:", error);
    }
  };

  const handleViewProposal = (proposal: Proposal) => {
    // Navigate to proposal details
    window.location.href = `/${slug}/proposals/${proposal.id}`;
  };

  const uiProposals = proposals.map(adaptApiProposalToUI);

  return (
    <CanRead entity={DEFAULT_ENTITIES.PROPOSAL} resourceId={personalizedRoute}>
      <Listing className="p-6">
        <ProposalHeader
          title="Proposals"
          subtitle={`Manage your client proposals, ${proposals.length} ${
            proposals.length === 1 ? "proposal" : "proposals"
          }`}
          onCreateProposal={() => setShowForm(true)}
        />

        <ProposalStatistics statistics={statistics} isLoading={isLoading} />

        <ProposalTable
          proposals={uiProposals}
          isLoading={isLoading}
          onView={handleViewProposal}
        />

        {showForm && (
          <ProposalForm
            onSubmit={handleCreateProposal}
            onCancel={() => setShowForm(false)}
            isLoading={isLoading}
          />
        )}
      </Listing>
    </CanRead>
  );
}

// Export compound component with dot notation
export const Proposals = Object.assign(ProposalsContainer, {
  Header: ProposalHeader,
  Statistics: ProposalStatistics,
  Table: ProposalTable,
  Form: ProposalForm,
});
