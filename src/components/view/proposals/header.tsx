import { But<PERSON> } from "@/components/common/ui/button";
import { RBACWrapper } from "@/components/common/rbac/RBACWrapper";
import { DEFAULT_ENTITIES, PERMISSION_ACTIONS } from "@/lib/rbac";
import { Plus } from "lucide-react";
import { Listing } from "@/layouts/dashboard/basic";

interface ProposalHeaderProps {
  title: string;
  subtitle: string;
  onCreateProposal?: () => void;
}

export function ProposalHeader({
  title,
  subtitle,
  onCreateProposal,
}: ProposalHeaderProps) {
  return (
    <Listing.Header
      title={title}
      caption={subtitle}
      actions={
        onCreateProposal && (
          <RBACWrapper
            entity={DEFAULT_ENTITIES.PROPOSAL}
            action={PERMISSION_ACTIONS.CREATE}
          >
            <Button onClick={onCreateProposal}>
              <Plus className="h-4 w-4 mr-2" />
              New Proposal
            </Button>
          </RBACWrapper>
        )
      }
    />
  );
}
