import { useEffect, useState } from "react";
import { But<PERSON> } from "@/components/common/ui/button";
import { Input } from "@/components/common/ui/input";
import {
  <PERSON><PERSON>,
  DialogContent,
  Di<PERSON>Header,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>ger,
  Di<PERSON>Footer,
} from "@/components/common/ui/dialog";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>ger,
  TabsContent,
} from "@/components/common/ui/tabs";
import { Plus, Trash2 } from "lucide-react";
import type { Milestone } from "@/data/proposals-mock";
import { Checkbox } from "@/components/common/ui/checkbox";
import { Label } from "@/components/common/ui/label";
import { useDocumentHandler } from "@/components/common/documents";
import RichTextEditor from "@/components/common/richtext";

interface ProposalFormProps {
  editMode?: boolean;
  data?: CreateProposalFormData;
  isOpen: boolean;
  onOpenChange: (open: boolean) => void;
  onSubmit: (formData: CreateProposalFormData) => void;
  isCreating: boolean;
  isEditing: boolean;
}

export interface CreateProposalFormData {
  id: string;
  createdAt: Date;
  updatedAt: Date;
  name: string;
  description: string;
  budgetType: "fixed" | "milestone";
  totalBudget: number;
  fixedBudget: number;
  milestones: Milestone[];
  duration: number;
  agreedToTerms: boolean;
  links: string[];
}

const INITIAL_FORMDATA: CreateProposalFormData = {
  id: "",
  createdAt: new Date(),
  updatedAt: new Date(),
  totalBudget: 0,
  name: "",
  description: "",
  budgetType: "fixed",
  fixedBudget: 0,
  milestones: [{ index: 1, amount: 0, description: "" }],
  duration: 1,
  agreedToTerms: false,
  links: [],
};

export function CreateProposalDialog({
  editMode,
  data,
  isOpen,
  onOpenChange,
  onSubmit,
  isCreating,
  isEditing,
}: ProposalFormProps) {
  const [formData, setFormData] =
    useState<CreateProposalFormData>(INITIAL_FORMDATA);
  const {
    selectedFiles,
    selectedLibrary,
    DocumentHandler,
    setSelectedFiles,
    setSelectedLibrary,
  } = useDocumentHandler();

  const addMilestone = () => {
    setFormData({
      ...formData,
      milestones: [
        ...formData.milestones,
        { index: formData.milestones.length + 1, amount: 0, description: "" },
      ],
    });
  };

  const removeMilestone = (index: number) => {
    setFormData({
      ...formData,
      milestones: formData.milestones.filter((_, i) => i !== index),
    });
  };

  const updateMilestone = (
    index: number,
    field: keyof Milestone,
    value: string | number
  ) => {
    const updatedMilestones = formData.milestones.map((milestone, i) =>
      i === index ? { ...milestone, [field]: value } : milestone
    );
    setFormData({ ...formData, milestones: updatedMilestones });
  };

  const calculateTotalBudget = () => {
    if (formData.budgetType === "fixed") {
      return formData.fixedBudget;
    }
    return formData.milestones.reduce(
      (total, milestone) => total + milestone.amount,
      0
    );
  };

  // Attachment functionality removed as it's not part of the Prisma schema
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    let data: any = formData;

    if (selectedFiles?.length > 0) {
      let formData = new FormData();
      selectedFiles.forEach((file: File, index: number) => {
        let count: number = index + 1;
        formData.append("attachment-" + count, file, file.name);
      });

      Object.entries(data).forEach(([key, value]: [string, any]) => {
        formData.append(key, value);
      });

      data = formData;
    }

    if (selectedLibrary?.length > 0) {
      data.attachment = { id: selectedLibrary };
    }

    onSubmit(data);

    // Reset form
    setFormData(INITIAL_FORMDATA);
  };

  useEffect(() => {
    // Set Data if present
    if (data) {
      setFormData(data);
    }
  }, [isOpen, data]);

  return (
    <Dialog open={isOpen} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-3xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>{editMode ? "Edit" : "Create"} New Proposal</DialogTitle>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="flex flex-col gap-8 mt-8">
          {/* Basic Information */}
          <div className="space-y-2">
            <Label htmlFor="proposal-name">Proposal Name</Label>
            <Input
              id="proposal-name"
              value={formData.name}
              onChange={(e) =>
                setFormData({ ...formData, name: e.target.value })
              }
              placeholder="Enter proposal name"
              required
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="proposal-description">Description</Label>
            <RichTextEditor
              id="proposal-description"
              className="h-[30em] min-h-[35em] mb-8"
              value={formData.description}
              theme="snow"
              onChange={(content) =>
                setFormData({ ...formData, description: content })
              }
              placeholder="Enter proposal description"
            />
          </div>

          {/* Budget Type Selection */}
          <div className="space-y-2">
            <Label>Budget Type</Label>
            <Tabs
              value={formData.budgetType}
              className="space-y-5"
              onValueChange={(value) =>
                setFormData({
                  ...formData,
                  budgetType: value as "fixed" | "milestone",
                })
              }
            >
              <TabsList className="grid w-full grid-cols-2">
                <TabsTrigger value="fixed">Total Budget</TabsTrigger>
                <TabsTrigger value="milestone">Milestone Based</TabsTrigger>
              </TabsList>
              <TabsContent value="fixed">
                <div className="space-y-2">
                  <Label htmlFor="fixed-budget">Total Budget ($)</Label>
                  <Input
                    id="fixed-budget"
                    type="number"
                    value={formData.fixedBudget}
                    onChange={(e) =>
                      setFormData({
                        ...formData,
                        fixedBudget: Number(e.target.value),
                      })
                    }
                    placeholder="Enter fixed budget amount"
                    min="0"
                    required
                  />
                </div>
              </TabsContent>

              {/* Budget Details */}
              <TabsContent value="milestone">
                <div>
                  <div className="flex items-center justify-between mb-4">
                    <Label>Milestones</Label>
                    <Button
                      type="button"
                      onClick={addMilestone}
                      variant="outline"
                      size="sm"
                    >
                      <Plus className="h-4 w-4 mr-1" />
                      Add Milestone
                    </Button>
                  </div>

                  <div className="space-y-4">
                    {formData.milestones.map((milestone, index) => (
                      <div
                        key={index}
                        className="border rounded-lg p-4 space-y-3"
                      >
                        <div className="flex items-center justify-between">
                          <span className="text-sm font-medium text-gray-700">
                            Milestone {milestone.index}
                          </span>
                          {formData.milestones.length > 1 && (
                            <Button
                              type="button"
                              variant="ghost"
                              size="sm"
                              onClick={() => removeMilestone(index)}
                            >
                              <Trash2 className="h-4 w-4" />
                            </Button>
                          )}
                        </div>

                        <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                          <div className="space-y-1">
                            <Label
                              htmlFor={`milestone-amount-${index}`}
                              className="text-xs"
                            >
                              Amount ($)
                            </Label>
                            <Input
                              id={`milestone-amount-${index}`}
                              type="number"
                              value={milestone.amount}
                              onChange={(e) =>
                                updateMilestone(
                                  index,
                                  "amount",
                                  Number(e.target.value)
                                )
                              }
                              placeholder="Amount"
                              min="0"
                              required
                            />
                          </div>
                          <div className="space-y-1">
                            <Label
                              htmlFor={`milestone-description-${index}`}
                              className="text-xs"
                            >
                              Description
                            </Label>
                            <Input
                              id={`milestone-description-${index}`}
                              value={milestone.description}
                              onChange={(e) =>
                                updateMilestone(
                                  index,
                                  "description",
                                  e.target.value
                                )
                              }
                              placeholder="Milestone description"
                              required
                            />
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              </TabsContent>
            </Tabs>
          </div>

          {/* Duration */}
          <div className="space-y-2">
            <Label htmlFor="duration">Duration (weeks)</Label>
            <Input
              id="duration"
              type="number"
              value={formData.duration}
              onChange={(e) =>
                setFormData({ ...formData, duration: Number(e.target.value) })
              }
              placeholder="Project duration in weeks"
              min="1"
              required
            />
          </div>

          {/* Total Budget Display */}
          <div className="bg-gray-50 p-4 rounded-lg">
            <div className="flex justify-between items-center">
              <span className="text-sm font-medium text-gray-700">
                Total Budget:
              </span>
              <span className="text-lg font-bold text-gray-900">
                ${calculateTotalBudget().toLocaleString()}
              </span>
            </div>
          </div>

          {/* Document Attachments */}
          <div className="border-2 border-dashed border-gray-300 dark:border-gray-600 rounded-lg p-4">
            <DocumentHandler
              setSelectedFiles={setSelectedFiles}
              setSelectedLibrary={setSelectedLibrary}
            />
          </div>

          {/* Terms and Conditions */}
          <div className="flex flex-row items-center gap-2">
            <Checkbox
              id="terms"
              checked={formData.agreedToTerms}
              onCheckedChange={(checked) =>
                setFormData({ ...formData, agreedToTerms: !!checked })
              }
              required
            />
            <Label htmlFor="terms" className="text-sm leading-relaxed">
              I agree to the{" "}
              <a href="#" className="text-blue-600 hover:underline">
                Terms & Conditions
              </a>
            </Label>
          </div>

          <DialogFooter>
            <Button
              type="button"
              variant="outline"
              onClick={() => onOpenChange(false)}
            >
              Cancel
            </Button>
            <Button
              type="submit"
              className="bg-blue-600 hover:bg-blue-700"
              disabled={isCreating}
            >
              {editMode
                ? `${isEditing ? "Editing..." : "Edit Proposal"}`
                : `${isCreating ? "Creating..." : "Create Proposal"}`}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
}
