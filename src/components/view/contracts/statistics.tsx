"use client";

import {
  <PERSON>Text,
  CheckCircle,
  Clock,
  XCircle,
  DollarSign,
  TrendingUp,
} from "lucide-react";
import { Listing } from "@/layouts/dashboard/basic";

interface ContractStatistics {
  total: number;
  active: number;
  completed: number;
  draft: number;
  terminated: number;
  totalValue: number;
  averageValue: number;
}

interface ContractStatisticsProps {
  statistics: ContractStatistics;
  isLoading: boolean;
}

export function ContractStatistics({
  statistics,
  isLoading,
}: ContractStatisticsProps) {
  return (
    <Listing.Statistics>
      <Listing.StatCard
        icon={FileText}
        name="Total Contracts"
        value={statistics.total}
        color="blue"
        loading={isLoading}
      />
      <Listing.StatCard
        icon={Clock}
        name="Active"
        value={statistics.active}
        color="green"
        loading={isLoading}
      />
      <Listing.StatCard
        icon={CheckCircle}
        name="Completed"
        value={statistics.completed}
        color="primary"
        loading={isLoading}
      />
      <Listing.StatCard
        icon={XCircle}
        name="Terminated"
        value={statistics.terminated}
        color="red"
        loading={isLoading}
      />
      <Listing.StatCard
        icon={DollarSign}
        name="Total Value"
        value={statistics.totalValue}
        valueType="dollar"
        color="purple"
        loading={isLoading}
      />
    </Listing.Statistics>
  );
}
