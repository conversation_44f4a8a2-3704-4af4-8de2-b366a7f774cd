"use client";

import { useState, useEffect } from "react";
import { useParams } from "next/navigation";
import { useAuth } from "@/hooks/useAuth";
import { useContracts } from "@/hooks/useContracts";
import { CanRead } from "@/components/common/rbac/RBACWrapper";
import { DEFAULT_ENTITIES } from "@/lib/rbac";
import { Listing } from "@/layouts/dashboard/basic";
import { type Contract } from "@/data/contracts-mock";
import type {
  Contract as ApiContract,
  CreateContract,
} from "@/lib/api/validators/schemas/contract";
import { ContractCreateDialog } from "./create-dialog";
import { ContractHeader } from "./header";
import { ContractStatistics } from "./statistics";
import { ContractTable } from "./table";

// Type adapter to convert API contracts to UI contracts
const adaptApiContractToUI = (apiContract: ApiContract): Contract => {
  const statusMap: Record<string, Contract["status"]> = {
    draft: "draft",
    active: "active",
    completed: "completed",
    terminated: "terminated",
    expired: "expired",
  };

  return {
    id: apiContract.id,
    title: `Contract ${apiContract.id}`,
    description: "",
    status: statusMap[apiContract.status] || "draft",
    clientName: apiContract.client?.name || "",
    contractValue: apiContract.total_value || 0,
    startDate: new Date(apiContract.start_date).toISOString(),
    endDate: new Date(apiContract.end_date).toISOString(),
    createdDate: new Date(apiContract.createdAt).toISOString(),
    lastModified: new Date(apiContract.updatedAt).toISOString(),
    proposalId: apiContract.proposal_id,
  };
};

// Main Container Component
function ContractsContainer() {
  const { slug } = useParams();
  const { personalizedRoute } = useAuth();

  const {
    contracts,
    statistics,
    isLoading,
    error,
    createContract,
    updateContract,
    deleteContract,
    clearError,
    initializeContracts,
  } = useContracts();

  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);

  useEffect(() => {
    initializeContracts();
  }, [slug, initializeContracts]);

  useEffect(() => {
    if (error) {
      console.error("Contract error:", error);
    }
  }, [error]);

  useEffect(() => {
    return () => {
      clearError();
    };
  }, [clearError]);

  const handleCreateContract = async (formData: CreateContract) => {
    try {
      await createContract(formData);
      setIsCreateDialogOpen(false);
    } catch (error) {
      console.error("Failed to create contract:", error);
    }
  };

  const handleUpdateContract = async (
    id: string,
    data: Partial<ApiContract>
  ) => {
    try {
      await updateContract(id, data);
    } catch (error) {
      console.error("Failed to update contract:", error);
    }
  };

  const handleDeleteContract = async (id: string) => {
    try {
      await deleteContract(id);
    } catch (error) {
      console.error("Failed to delete contract:", error);
    }
  };

  const uiContracts = contracts.map(adaptApiContractToUI);

  return (
    <CanRead entity={DEFAULT_ENTITIES.CONTRACT} resourceId={personalizedRoute}>
      <Listing className="p-6">
        <ContractHeader
          onCreateContract={() => setIsCreateDialogOpen(true)}
          contractsCount={contracts.length}
        />

        <ContractStatistics statistics={statistics} isLoading={isLoading} />

        <ContractTable
          contracts={uiContracts}
          isLoading={isLoading}
          onUpdateContract={handleUpdateContract}
          onDeleteContract={handleDeleteContract}
        />

        <ContractCreateDialog
          open={isCreateDialogOpen}
          onOpenChange={setIsCreateDialogOpen}
          onCreateContract={handleCreateContract}
          isLoading={isLoading}
        />
      </Listing>
    </CanRead>
  );
}

// Export compound component with dot notation
export const Contracts = Object.assign(ContractsContainer, {
  Header: ContractHeader,
  Statistics: ContractStatistics,
  Table: ContractTable,
});
