"use client";

import { FileText, HardDrive } from "lucide-react";
import { Listing } from "@/layouts/dashboard/basic";
import type { DocumentStatistics } from "@/data/documents-mock";

interface DocumentStatisticsProps {
  statistics: DocumentStatistics;
  isLoading: boolean;
}

const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return "0 B";
  const k = 1024;
  const sizes = ["B", "KB", "MB", "GB"];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
};

export function DocumentStatistics({
  statistics,
  isLoading,
}: DocumentStatisticsProps) {
  return (
    <Listing.Statistics>
      <Listing.StatCard
        icon={FileText}
        name="Total Documents"
        value={statistics?.total || 0}
        color="blue"
        loading={isLoading}
      />
      <Listing.StatCard
        icon={HardDrive}
        name="Total Size"
        value={formatFileSize(statistics?.totalSize || 0)}
        color="purple"
        loading={isLoading}
      />
    </Listing.Statistics>
  );
}
