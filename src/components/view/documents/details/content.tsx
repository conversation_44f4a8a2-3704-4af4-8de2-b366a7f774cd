"use client";

import React from "react";
import dynamic from "next/dynamic";

const DocViewer = dynamic(() => import("react-doc-viewer"), {
  ssr: false,
});
import { DocViewerRenderers, IDocument } from "react-doc-viewer";

import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
} from "@/components/common/ui/card";
import { FileText } from "lucide-react";

import { CustomPDFRender } from "@/hooks/useDocumentPreview";
import type { Document } from "@/lib/api/validators/schemas/document";

interface DocumentDetailsContentProps {
  document: Document;
}

export function DocumentDetailsContent({
  document,
}: DocumentDetailsContentProps) {
  const docs: IDocument[] = document
    ? [
        {
          uri: document.path,
          fileType: document.file_type || "application/pdf",
        },
      ]
    : [];

  return (
    <Card className="h-full">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <FileText className="h-5 w-5" />
          Preview
        </CardTitle>
      </CardHeader>
      <CardContent className="h-full space-y-6">
        {docs?.length > 0 && (
          <DocViewer
            documents={docs}
            pluginRenderers={[CustomPDFRender, ...DocViewerRenderers]}
            config={{
              header: {
                disableHeader: true,
              },
            }}
            style={{
              height: "90vh",
              width: "100%",
              border: 0,
              appearance: "none",
            }}
          />
        )}
      </CardContent>
    </Card>
  );
}
