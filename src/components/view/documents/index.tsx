"use client";

import { useState, useEffect } from "react";
import { useParams } from "next/navigation";
import { useAuth } from "@/hooks/useAuth";
import { useDocuments } from "@/hooks/useDocuments";
import { CanRead } from "@/components/common/rbac/RBACWrapper";
import { DEFAULT_ENTITIES } from "@/lib/rbac";
import { Listing } from "@/layouts/dashboard/basic";
import type { Document } from "@/lib/api/validators/schemas/document";
import type { Document as MockDocument } from "@/data/documents-mock";
import { DocumentHeader } from "./header";
import { DocumentStatistics } from "./statistics";
import { DocumentTable } from "./table";
import { DocumentCreateDialog } from "./create-dialog";

// Adapter function to convert API document to UI document
const adaptApiDocumentToUI = (apiDocument: Document): MockDocument => ({
  id: apiDocument.id,
  name: apiDocument.name,
  path: apiDocument.path,
  file_type: apiDocument.file_type,
  size: apiDocument.size,
  status: apiDocument.status,
  category: apiDocument.category,
  association_entity: apiDocument.association_entity,
  association_id: apiDocument.association_id,
  proposalId: apiDocument.proposalId,
  createdAt: new Date(apiDocument.createdAt).toISOString(),
  updatedAt: new Date(apiDocument.updatedAt).toISOString(),
});

// Main Container Component
function DocumentsContainer() {
  const { slug } = useParams();
  const { personalizedRoute } = useAuth();

  const {
    documents,
    statistics,
    isLoading,
    isCreating,
    error,
    create,
    update,
    remove,
    clearError,
  } = useDocuments();

  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);

  useEffect(() => {
    if (error) {
      console.error("Document error:", error);
    }
  }, [error]);

  useEffect(() => {
    return () => {
      clearError();
    };
  }, [clearError]);

  const handleCreateDocument = async (formData: FormData) => {
    try {
      await create(formData);
      setIsCreateDialogOpen(false);
    } catch (error) {
      console.error("Failed to create document:", error);
    }
  };

  const handleUpdateDocument = async (id: string, data: any) => {
    try {
      await update(id, data);
    } catch (error) {
      console.error("Failed to update document:", error);
    }
  };

  const handleDeleteDocument = async (id: string) => {
    try {
      await remove(id);
    } catch (error) {
      console.error("Failed to delete document:", error);
    }
  };

  const uiDocuments = documents.map(adaptApiDocumentToUI);

  return (
    <CanRead entity={DEFAULT_ENTITIES.DOCUMENT} resourceId={personalizedRoute}>
      <Listing className="p-6">
        <DocumentHeader
          onCreateDocument={() => setIsCreateDialogOpen(true)}
          documentsCount={documents.length}
        />

        <DocumentStatistics statistics={statistics} isLoading={isLoading} />

        <DocumentTable
          documents={uiDocuments}
          isLoading={isLoading}
          onUpdateDocument={handleUpdateDocument}
          onDeleteDocument={handleDeleteDocument}
        />

        <DocumentCreateDialog
          open={isCreateDialogOpen}
          onOpenChange={setIsCreateDialogOpen}
          onCreateDocument={handleCreateDocument}
          isLoading={isCreating}
        />
      </Listing>
    </CanRead>
  );
}

// Export compound component with dot notation
export const Documents = Object.assign(DocumentsContainer, {
  Header: DocumentHeader,
  Statistics: DocumentStatistics,
  Table: DocumentTable,
  CreateDialog: DocumentCreateDialog,
});
