"use client";

import { Badge } from "@/components/common/ui/badge";
import { But<PERSON> } from "@/components/common/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/common/ui/dropdown-menu";
import {
  MoreHorizontal,
  Eye,
  Trash2,
  Download,
  FileText,
  Image,
  File,
} from "lucide-react";
import { format } from "date-fns";
import { Listing } from "@/layouts/dashboard/basic";
import type { Document } from "@/data/documents-mock";
import { useDocuments } from "@/hooks/useDocuments";
import {
  useDocumentPreview,
  DocumentPreviewDialog,
} from "@/hooks/useDocumentPreview";

interface DocumentTableProps {
  documents: Document[];
  isLoading: boolean;
  onUpdateDocument: (id: string, data: any) => void;
  onDeleteDocument: (id: string) => void;
}

const getStatusColor = (status: Document["status"]) => {
  switch (status) {
    case "created":
      return "bg-gray-100 text-gray-800";
    case "submitted":
      return "bg-blue-100 text-blue-800";
    case "received":
      return "bg-purple-100 text-purple-800";
    case "negotiating":
      return "bg-orange-100 text-orange-800";
    case "agreed":
      return "bg-green-100 text-green-800";
    case "inprogress":
      return "bg-yellow-100 text-yellow-800";
    case "reviewing":
      return "bg-indigo-100 text-indigo-800";
    case "completed":
      return "bg-emerald-100 text-emerald-800";
    default:
      return "bg-gray-100 text-gray-800";
  }
};

const getFileIcon = (fileType: string) => {
  if (fileType.startsWith("image/")) {
    return Image;
  } else if (fileType === "application/pdf") {
    return FileText;
  } else {
    return File;
  }
};

const formatFileSize = (bytes: string): string => {
  const numBytes = parseInt(bytes);
  if (numBytes === 0) return "0 B";
  const k = 1024;
  const sizes = ["B", "KB", "MB", "GB"];
  const i = Math.floor(Math.log(numBytes) / Math.log(k));
  return parseFloat((numBytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
};

export function DocumentTable({
  documents,
  isLoading,
  onUpdateDocument,
  onDeleteDocument,
}: DocumentTableProps) {
  const { downloadDocument } = useDocuments();
  const { isOpen, document, openPreview, closePreview } = useDocumentPreview();

  const handleView = (document: Document) => {
    // Open preview dialog for quick view
    openPreview({
      uri: document.path,
      fileType: document.file_type,
    });
  };

  const handleDownload = async (document: any) => {
    try {
      await downloadDocument(document);
    } catch (error) {
      console.error("Failed to download document:", error);
    }
  };

  const handleDelete = async (documentId: string) => {
    try {
      await onDeleteDocument(documentId);
    } catch (error) {
      console.error("Failed to delete document:", error);
    }
  };

  const columns = [
    {
      key: "name",
      label: "Document Name",
      render: (document: Document) => {
        const FileIcon = getFileIcon(document.file_type);
        return (
          <div className="flex items-center space-x-3">
            <FileIcon className="h-5 w-5 text-gray-400" />
            <div>
              <div className="font-medium text-foreground">{document.name}</div>
              <div className="text-sm text-muted-foreground">
                {document.file_type}
              </div>
            </div>
          </div>
        );
      },
    },
    {
      key: "size",
      label: "Size",
      render: (document: Document) => (
        <span className="text-sm text-foreground">
          {formatFileSize(document.size)}
        </span>
      ),
    },
    {
      key: "status",
      label: "Status",
      render: (document: Document) => (
        <Badge variant="secondary" className={getStatusColor(document.status)}>
          {document.status.charAt(0).toUpperCase() + document.status.slice(1)}
        </Badge>
      ),
    },
    {
      key: "createdAt",
      label: "Created",
      render: (document: Document) => (
        <span className="text-sm text-foreground">
          {format(new Date(document.createdAt), "MMM dd, yyyy")}
        </span>
      ),
    },
    {
      key: "actions",
      label: "Actions",
      render: (document: Document) => (
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="ghost" className="h-8 w-8 p-0">
              <MoreHorizontal className="h-4 w-4" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            <DropdownMenuItem onClick={() => handleView(document)}>
              <Eye className="mr-2 h-4 w-4" />
              Preview
            </DropdownMenuItem>
            <DropdownMenuItem onClick={() => handleDownload(document)}>
              <Download className="mr-2 h-4 w-4" />
              Download
            </DropdownMenuItem>
            <DropdownMenuItem
              onClick={() => handleDelete(document.id)}
              className="text-red-600"
            >
              <Trash2 className="mr-2 h-4 w-4" />
              Delete
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      ),
    },
  ];

  return (
    <>
      <Listing.Table
        id="documents-table"
        data={documents}
        columns={columns}
        loading={isLoading}
        emptyState={
          <div className="text-center py-12">
            <FileText className="mx-auto h-12 w-12 text-gray-400" />
            <h3 className="mt-2 text-sm font-medium text-gray-900 dark:text-white">
              No documents
            </h3>
            <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
              Get started by uploading your first document.
            </p>
          </div>
        }
      />

      {/* Document Preview Dialog */}
      {document && (
        <DocumentPreviewDialog
          isOpen={isOpen}
          onClose={closePreview}
          document={document}
          title="Document Preview"
          onDownload={() => {
            if (document) {
              // Extract document from the documents array
              const foundDocument = documents.find(
                (d) => d.path === document.uri
              );
              if (foundDocument) {
                handleDownload(foundDocument);
              }
            }
          }}
        />
      )}
    </>
  );
}
