"use client";

import React from "react";
import { Card, CardContent } from "@/components/common/ui/card";
import { Button } from "@/components/common/ui/button";
import { mockScheduleEvents, mockWorkingFormat } from "@/data/dashboard-mock";
import {
  ChartContainer,
  ChartTooltip,
  ChartTooltipContent,
} from "@/components/common/ui/chart";
import { Bar, BarChart } from "recharts";
import { Calendar } from "@/components/common/ui/calendar";
import { QuickStats } from "./quickstats";
import {
  Ta<PERSON>,
  TabsList,
  TabsTrigger,
  TabsContent,
} from "@/components/common/ui/tabs";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/common/ui/table";
import { useDashboard } from "@/hooks/useDashboard";

// Types for better type safety
interface WorkingFormatBarProps {
  title: string;
  percentage: number;
  color: string;
}

// Chart configuration
const chartConfig = {
  value: {
    label: "Percentage",
    color: "hsl(var(--primary))",
  },
};

// Working Format Bar Component
const WorkingFormatBar = ({
  title,
  percentage,
  color,
}: WorkingFormatBarProps) => {
  const chartData = [{ name: title, value: percentage }];

  return (
    <div className="w-full relative">
      <div className="bottom-[15%] left-[25%] -translate-x-[25%] absolute flex flex-col items-start z-10">
        <span className="text-sm text-foreground capitalize">{title}</span>
        <span className="text-xl font-bold text-foreground">{percentage}%</span>
      </div>
      <ChartContainer config={chartConfig} className="h-full w-full">
        <BarChart data={chartData} layout="horizontal">
          <Bar
            dataKey="value"
            fill={color}
            radius={[12, 12, 0, 0]}
            maxBarSize={800}
          />
          <ChartTooltip
            cursor={false}
            content={<ChartTooltipContent hideLabel />}
          />
        </BarChart>
      </ChartContainer>
    </div>
  );
};

// Working Format Header Component
const WorkingFormatHeader = () => {
  return (
    <div className="flex items-center justify-between mb-4">
      <h3 className="text-sm font-medium text-foreground">Working format</h3>
      <span className="text-xs text-muted-foreground">Details</span>
    </div>
  );
};

// Working Format Charts Component
const WorkingFormatCharts = () => {
  return (
    <div className="flex flex-row items-end justify-between">
      <WorkingFormatBar
        title="hybrid"
        percentage={mockWorkingFormat.hybrid}
        color="hsl(var(--primary))"
      />
      <WorkingFormatBar
        title="remote"
        percentage={mockWorkingFormat.remote}
        color="hsl(var(--muted-foreground))"
      />
    </div>
  );
};

const UpcomingSchedules = () => {
  return (
    <Card className="w-full h-full bg-card border-border">
      <CardContent className="flex flex-col gap-8">
        <h3 className="text-sm font-medium text-foreground">Upcoming</h3>
        <div className="flex flex-col gap-3">
          {mockScheduleEvents.map((event: any, index: number) => {
            return (
              <div
                key={index}
                className="bg-muted/50 p-4 rounded-lg border-l-4 border-primary flex items-center justify-between"
              >
                <span className="text-sm text-foreground">{event.title}</span>
                <span className="text-xs text-muted-foreground">
                  {event.time}
                </span>
              </div>
            );
          })}
        </div>
      </CardContent>
    </Card>
  );
};

// Working Format Card Component
const WorkingFormatCard = () => {
  return (
    <Card className="w-full bg-card border-border">
      <CardContent className="p-6">
        <WorkingFormatHeader />
        <WorkingFormatCharts />
      </CardContent>
    </Card>
  );
};

// Calendar Card Component
const CalendarCard = () => {
  return (
    <Card className="w-full bg-card border-border">
      <CardContent className="p-6">
        <Calendar
          mode="single"
          selected={new Date()}
          onSelect={(date) => console.log(date)}
          buttonVariant="outline"
        />
      </CardContent>
    </Card>
  );
};

// Dashboard Summary Cards Component
const DashboardSummaryCards = () => {
  const { summary, contractStatistics, proposalStatistics, isLoading } =
    useDashboard();

  return (
    <div className="grid grid-cols-2 gap-4 mb-6">
      {/* Contracts Summary Card */}
      <Card className="bg-card border-border">
        <CardContent className="p-6">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-sm font-medium text-foreground">Contracts</h3>
            <div className="w-8 h-8 bg-primary/20 rounded-lg flex items-center justify-center">
              <div className="w-4 h-4 bg-primary rounded"></div>
            </div>
          </div>
          <div className="space-y-2">
            <div className="text-3xl font-bold text-foreground">
              {isLoading ? "..." : summary?.contractsCount || 0}
            </div>
            <div className="text-sm text-muted-foreground">Total contracts</div>
            {contractStatistics && (
              <div className="flex items-center gap-4 text-xs text-muted-foreground/70">
                <span>Active: {contractStatistics.active || 0}</span>
                <span>Completed: {contractStatistics.completed || 0}</span>
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Proposals Summary Card */}
      <Card className="bg-card border-border">
        <CardContent className="p-6">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-sm font-medium text-foreground">Proposals</h3>
            <div className="w-8 h-8 bg-secondary/20 rounded-lg flex items-center justify-center">
              <div className="w-4 h-4 bg-secondary rounded"></div>
            </div>
          </div>
          <div className="space-y-2">
            <div className="text-3xl font-bold text-foreground">
              {isLoading ? "..." : summary?.proposalsCount || 0}
            </div>
            <div className="text-sm text-muted-foreground">Total proposals</div>
            {proposalStatistics && (
              <div className="flex items-center gap-4 text-xs text-muted-foreground/70">
                <span>Pending: {proposalStatistics.pending || 0}</span>
                <span>Approved: {proposalStatistics.approved || 0}</span>
              </div>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

// Recent Contracts Table Component
const RecentContractsTable = () => {
  const { recentContracts, isLoadingContracts } = useDashboard();

  if (isLoadingContracts) {
    return (
      <div className="text-center text-muted-foreground py-8">
        Loading recent contracts...
      </div>
    );
  }

  return (
    <Table>
      <TableHeader>
        <TableRow className="border-border">
          <TableHead className="text-muted-foreground">Contract</TableHead>
          <TableHead className="text-muted-foreground">Status</TableHead>
          <TableHead className="text-muted-foreground">Value</TableHead>
          <TableHead className="text-muted-foreground">Date</TableHead>
        </TableRow>
      </TableHeader>
      <TableBody>
        {recentContracts.length === 0 ? (
          <TableRow>
            <TableCell
              colSpan={4}
              className="text-center text-muted-foreground py-8"
            >
              No recent contracts found
            </TableCell>
          </TableRow>
        ) : (
          recentContracts.map((contract: any) => (
            <TableRow
              key={contract.id}
              className="border-border hover:bg-muted/50"
            >
              <TableCell className="text-foreground">
                <div>
                  <div className="font-medium">
                    {contract.title || contract.name}
                  </div>
                  <div className="text-sm text-muted-foreground truncate max-w-[200px]">
                    {contract.description}
                  </div>
                </div>
              </TableCell>
              <TableCell>
                <span
                  className={`px-2 py-1 rounded-full text-xs font-medium ${
                    contract.status === "active"
                      ? "bg-lime-500/20 text-lime-400"
                      : contract.status === "completed"
                      ? "bg-green-500/20 text-green-400"
                      : contract.status === "draft"
                      ? "bg-yellow-500/20 text-yellow-400"
                      : "bg-muted text-muted-foreground"
                  }`}
                >
                  {contract.status}
                </span>
              </TableCell>
              <TableCell className="text-foreground">
                ${contract.contract_value?.toLocaleString() || "0"}
              </TableCell>
              <TableCell className="text-muted-foreground">
                {new Date(contract.createdAt).toLocaleDateString()}
              </TableCell>
            </TableRow>
          ))
        )}
      </TableBody>
    </Table>
  );
};

// Recent Proposals Table Component
const RecentProposalsTable = () => {
  const { recentProposals, isLoadingProposals } = useDashboard();

  if (isLoadingProposals) {
    return (
      <div className="text-center text-muted-foreground py-8">
        Loading recent proposals...
      </div>
    );
  }

  return (
    <Table>
      <TableHeader>
        <TableRow className="border-border">
          <TableHead className="text-muted-foreground">Proposal</TableHead>
          <TableHead className="text-muted-foreground">Status</TableHead>
          <TableHead className="text-muted-foreground">Budget</TableHead>
          <TableHead className="text-muted-foreground">Date</TableHead>
        </TableRow>
      </TableHeader>
      <TableBody>
        {recentProposals.length === 0 ? (
          <TableRow>
            <TableCell
              colSpan={4}
              className="text-center text-muted-foreground py-8"
            >
              No recent proposals found
            </TableCell>
          </TableRow>
        ) : (
          recentProposals.map((proposal: any) => (
            <TableRow
              key={proposal.id}
              className="border-border hover:bg-muted/50"
            >
              <TableCell className="text-foreground">
                <div>
                  <div className="font-medium">
                    {proposal.name || proposal.title}
                  </div>
                  <div className="text-sm text-muted-foreground truncate max-w-[200px]">
                    {proposal.description}
                  </div>
                </div>
              </TableCell>
              <TableCell>
                <span
                  className={`px-2 py-1 rounded-full text-xs font-medium ${
                    proposal.status === "agreed" ||
                    proposal.status === "inprogress"
                      ? "bg-lime-500/20 text-lime-400"
                      : proposal.status === "completed"
                      ? "bg-green-500/20 text-green-400"
                      : proposal.status === "created" ||
                        proposal.status === "submitted"
                      ? "bg-blue-500/20 text-blue-400"
                      : proposal.status === "negotiating" ||
                        proposal.status === "reviewing"
                      ? "bg-yellow-500/20 text-yellow-400"
                      : proposal.status === "rejected"
                      ? "bg-red-500/20 text-red-400"
                      : "bg-muted text-muted-foreground"
                  }`}
                >
                  {proposal.status}
                </span>
              </TableCell>
              <TableCell className="text-foreground">
                $
                {proposal.total_budget?.toLocaleString() ||
                  proposal.fixed_budget?.toLocaleString() ||
                  "0"}
              </TableCell>
              <TableCell className="text-muted-foreground">
                {new Date(proposal.createdAt).toLocaleDateString()}
              </TableCell>
            </TableRow>
          ))
        )}
      </TableBody>
    </Table>
  );
};

const ProjectAndSummary = () => {
  return (
    <div className="space-y-6">
      <DashboardSummaryCards />

      {/* Recent Items Tabbed Section */}
      <Card className="bg-card border-border">
        <CardContent className="p-6">
          <div className="flex items-center justify-between mb-6">
            <h3 className="text-lg font-semibold text-foreground">
              Recent Activity
            </h3>
            <Button
              variant="ghost"
              size="sm"
              className="text-muted-foreground hover:text-foreground"
            >
              View All
            </Button>
          </div>

          <Tabs defaultValue="contracts" className="w-full">
            <TabsList className="bg-muted border-border mb-6">
              <TabsTrigger
                value="contracts"
                className="data-[state=active]:bg-primary data-[state=active]:text-primary-foreground"
              >
                Recent Contracts
              </TabsTrigger>
              <TabsTrigger
                value="proposals"
                className="data-[state=active]:bg-primary data-[state=active]:text-primary-foreground"
              >
                Recent Proposals
              </TabsTrigger>
            </TabsList>

            <TabsContent value="contracts" className="mt-0">
              <RecentContractsTable />
            </TabsContent>

            <TabsContent value="proposals" className="mt-0">
              <RecentProposalsTable />
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>
    </div>
  );
};

const SummaryReview = () => {
  return (
    <Tabs defaultValue="summary" className="flex flex-col gap-6">
      <TabsList className="bg-muted border-border">
        <TabsTrigger
          value="summary"
          className="data-[state=active]:bg-primary data-[state=active]:text-primary-foreground"
        >
          Summary
        </TabsTrigger>
        <TabsTrigger
          value="schedule"
          className="data-[state=active]:bg-lime-500 data-[state=active]:text-black"
        >
          Schedule
        </TabsTrigger>
      </TabsList>
      <TabsContent value="summary">
        <ProjectAndSummary />
      </TabsContent>
      <TabsContent value="schedule" className="flex flex-col gap-6">
        <span className="flex flex-col items-start gap-6">
          <WorkingFormatCard />
          <UpcomingSchedules />
        </span>
        <CalendarCard />
      </TabsContent>
    </Tabs>
  );
};

// Main Schedules Component using compound pattern
export const Summary = () => {
  return (
    <div className="flex flex-col gap-6">
      <QuickStats />
      <SummaryReview />
    </div>
  );
};
