"use client";

import React, {
  Fragment,
  useState,
  useCallback,
  useRef,
  useEffect,
} from "react";

import EmojiPicker from "emoji-picker-react";
import { Button } from "@/components/common/ui/button";
import { Send, Paperclip, Smile, Reply, CircleX } from "lucide-react";

import { cn } from "@/lib/utils";
import { useDocuments } from "@/hooks/useDocuments";
import RichTextEditor from "@/components/common/richtext";
import HtmlParser from "react-html-parser";

interface MessageComposerProps {
  toReply?: any;
  closeReply: () => void;
  onSendMessage: (content: string, toReply?: any, attachments?: any[]) => void;
  onStartTyping?: () => void;
  onStopTyping?: () => void;
  disabled?: boolean;
  placeholder?: string;
  className?: string;
}

function MessageReply({
  toReply,
  handleCloseReply,
}: {
  toReply: any;
  handleCloseReply: any;
}) {
  if (!toReply) return <></>;

  return (
    <div className="w-full z-10 flex items-center gap-4 text-white px-4 py-2 bg-linear-to-t from-black to-transparent">
      <Reply size={18} className="h-8 w-8 max-w-5 scale-[-1]" />
      <div className="max-h-[16em] origin-bottom overflow-y-scroll bg-lime-400 z-10 rounded-lg p-4">
        <p className="font-medium">
          Replying to {toReply.originalMessage.sender?.name}:{" "}
        </p>
        {HtmlParser(toReply.originalMessage.content)}
      </div>
      <CircleX
        size={18}
        className="h-8 w-8 max-w-5 cursor-pointer hover:text-lime-300"
        onClick={handleCloseReply}
      />
    </div>
  );
}

function MessageAttachments({
  attachments,
  removeAttachment,
}: {
  attachments: File[];
  removeAttachment: (index: number) => void;
}) {
  return (
    <div
      className="w-full flex flex-col gap-2 mx-1 p-4 border-l-5 border-lime-250"
      hidden={attachments.length === 0}
    >
      <p className="text-sm text-zinc-400">Attachments:</p>

      <div className="flex flex-wrap items-start gap-6">
        {attachments.map((file: any, index: number): any => (
          <div key={index} className="h-full flex flex-row items-center gap-2">
            <span className="text-sm text-zinc-600">
              {index + 1}. {file.name}
            </span>
            <CircleX
              className="h-3 w-3 mt-1 cursor-pointer text-zinc-400 hover:text-white duration-300"
              onClick={() => removeAttachment(index)}
            />
          </div>
        ))}
      </div>
    </div>
  );
}

export function MessageComposer({
  toReply,
  onSendMessage,
  closeReply,
  onStartTyping,
  onStopTyping,
  disabled = false,
  placeholder = "Type your message...",
  className,
}: MessageComposerProps) {
  const [content, setContent] = useState("");
  const [isTyping, setIsTyping] = useState(false);
  const typingTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const [attachments, setAttachments] = useState<File[]>([]);
  const [isEmojiPickerOpen, setIsEmojiPickerOpen] = useState(false);

  // Handle content change
  const handleChange = useCallback(
    (value: string) => {
      setContent(value);

      // Handle typing indicators
      if (value.trim() && !isTyping) {
        setIsTyping(true);
        onStartTyping?.();
      }

      // Clear existing timeout
      if (typingTimeoutRef.current) {
        clearTimeout(typingTimeoutRef.current);
      }

      // Set new timeout to stop typing
      typingTimeoutRef.current = setTimeout(() => {
        setIsTyping(false);
        onStopTyping?.();
      }, 1000);
    },
    [isTyping, onStartTyping, onStopTyping]
  );

  // Handle close reply
  const handleCloseReply = () => closeReply();

  function handleFilesChange(e: React.ChangeEvent<HTMLInputElement>) {
    const { files }: { files: any } = e.target ?? [];
    const list: File[] = Array.from(files);
    setAttachments(list);
  }

  function removeAttachment(index: number) {
    setAttachments((prev) => prev.filter((_, i) => i !== index));
  }

  // Handle send message
  const handleSend = useCallback(() => {
    const textContent = content.replace(/<[^>]*>/g, "").trim();

    if (!textContent || disabled) return;

    // Send message
    onSendMessage(content, toReply, attachments);
    setContent("");
    setIsTyping(false);
    onStopTyping?.();
    setAttachments([]);
    // Clear typing timeout
    if (typingTimeoutRef.current) {
      clearTimeout(typingTimeoutRef.current);
    }
  }, [content, disabled, onSendMessage, onStopTyping]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (typingTimeoutRef.current) {
        clearTimeout(typingTimeoutRef.current);
      }
    };
  }, []);

  const isEmpty = !content.replace(/<[^>]*>/g, "").trim();

  return (
    <div className={cn("message-composer", className)}>
      <div className="flex flex-col gap-3">
        {/* Rich text editor */}
        <div className="w-full relative">
          <MessageAttachments
            attachments={attachments}
            removeAttachment={removeAttachment}
          />

          <MessageReply toReply={toReply} handleCloseReply={handleCloseReply} />

          <RichTextEditor
            id="message-editor"
            className="h-[6em] relative border-t p-4"
            theme="bubble"
            value={content}
            onKeyDown={(e: any) => {
              if (e.key === "Enter" && !e.shiftKey) {
                handleSend();
              }
            }}
            onChange={handleChange}
            placeholder={placeholder}
          />
        </div>

        {/* 
          - TODO: Enable emoji picker later
          <EmojiPicker
            open={isEmojiPickerOpen}
            style={{
              position: "absolute",
              bottom: "100%",
              left: 0,
              zIndex: 1000,
            }}
          />
        */}

        {/* Actions */}
        <div className="flex items-center justify-between z-20 p-4">
          <div className="flex items-center gap-2">
            <Button
              variant="ghost"
              size="sm"
              className="h-8 w-8 p-0"
              disabled={disabled}
              onClick={() => {
                document.getElementById("chat-attachments")?.click();
              }}
            >
              <Paperclip className="h-4 w-4" />
              <input
                id="chat-attachments"
                type="file"
                multiple
                onChange={handleFilesChange}
                accept="image/*,video/*,audio/*,application/pdf,application/msword,application/vnd.openxmlformats-officedocument.wordprocessingml.document,text/plain"
                className="hidden"
              />
            </Button>

            {/* 
              - TODO: Enable emoji picker later
              <Button
                variant="ghost"
                size="sm"
                className="h-8 w-8 p-0"
                disabled={disabled}
                onClick={() => setIsEmojiPickerOpen(!isEmojiPickerOpen)}
              >
                <Smile className="h-4 w-4" />
              </Button> 
            */}
          </div>

          <Button
            onClick={handleSend}
            disabled={disabled || isEmpty}
            size="sm"
            className="gap-2"
            data-send-button
          >
            <Send className="h-4 w-4" />
            Send
          </Button>
        </div>
      </div>

      {/* Custom styles for chat quill */}
      <style jsx global>{`
        .message-composer .ql-toolbar {
          border-top: none;
          border-left: none;
          border-right: none;
          border-bottom: 1px solid #e5e7eb;
          padding: 8px 12px;
        }

        .message-composer .ql-container {
          border: none;
          font-size: 14px;
        }

        .message-composer .ql-editor {
          padding: 12px;
          min-height: 60px;
          max-height: 150px;
          overflow-y: auto;
        }

        .message-composer .ql-editor.ql-blank::before {
          font-style: normal;
          color: #9ca3af;
        }
      `}</style>
    </div>
  );
}
