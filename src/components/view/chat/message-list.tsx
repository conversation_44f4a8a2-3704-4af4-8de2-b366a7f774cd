"use client";

import React, { Fragment, useState, useEffect, useRef } from "react";
import {
  Avatar,
  AvatarFallback,
  AvatarImage,
} from "@/components/common/ui/avatar";
import { useDocuments } from "@/hooks/useDocuments";
import {
  DocumentPreviewDialog,
  useDocumentPreview,
} from "@/hooks/useDocumentPreview";
import { formatDistanceToNow } from "date-fns";
import { cn } from "@/lib/utils";
import HtmlParser from "react-html-parser";
import { Co<PERSON>, Reply, Pencil, Trash, Paperclip } from "lucide-react";
import {
  ContextMenu,
  ContextMenuTrigger,
  ContextMenuContent,
  ContextMenuItem,
  ContextMenuSeparator,
} from "@/components/common/ui/context-menu";
import { toast } from "sonner";

interface Message {
  id: string;
  content: string;
  sent_from: string;
  createdAt: Date;
  type: "text" | "image" | "file" | "form" | "reply";
  sender?: {
    id: string;
    name: string;
    email: string;
    avatar: string;
  };
  associations?: any[];
}

interface MessageListProps {
  messages: Message[];
  currentUserId?: string;
  isLoading?: boolean;
  className?: string;
  copyMessage: (message: any) => void;
  deleteMessage: (message: any) => void;
  updateMessage: (message: any, content: any) => void;
  replyToMessage: (message: any) => void;
}

interface MessageItemProps {
  message: Message;
  isOwn: boolean;
  showAvatar: boolean;
  copyMessage: (message: any) => void;
  deleteMessage: (message: any) => void;
  updateMessage: (message: any) => void;
  replyToMessage: (message: any) => void;
  showTimestamp: boolean;
}

function MessageAdormentPill({
  onClick,
  children,
  className,
  title,
}: {
  onClick?: () => void;
  children: React.ReactNode[];
  className?: string;
  title?: string;
}) {
  return (
    <span
      className={cn(
        "w-full h-auto min-w-[15em] text-xs font-medium bg-lime-400 text-white/70 rounded-lg cursor-pointer hover:bg-lime-450/60 hover:text-white transition-colors border-r-2 border-lime-250 hover:border-lime-200",
        className
      )}
      onClick={onClick}
      title={title}
    >
      {children}
    </span>
  );
}

function MessageReply({ association }: { association: any }) {
  const scrollToRepliedMessage = (message: any) => {
    if (message.type === "message" && !message.id) return;
    const repliedMessage = document.querySelector(`#message-${message.id}`);

    if (repliedMessage) {
      repliedMessage.scrollIntoView({ behavior: "smooth", block: "center" });

      // Add a brief highlight effect
      repliedMessage.classList.add("h-max");
      repliedMessage.classList.add("bg-zinc-900");
      repliedMessage.classList.add("rounded-lg");
      setTimeout(() => {
        repliedMessage.classList.remove("h-max");
        repliedMessage.classList.remove("bg-zinc-900");
        repliedMessage.classList.remove("rounded-lg");
      }, 2000);
    }
  };

  return (
    <MessageAdormentPill
      className="flex flex-col gap-2 p-2"
      onClick={() => scrollToRepliedMessage(association)}
    >
      <p className="capitalize font-semibold text-white/60">
        Replied: {association.sender}
      </p>
      {HtmlParser(association.content)}
    </MessageAdormentPill>
  );
}

function MessageAttachmentThumbnail({ attachment }: { attachment: any }) {
  const { fetchSignedURL } = useDocuments();
  const [path, setPath] = useState<string>("");

  async function fetchPath() {
    try {
      setPath(await fetchSignedURL(attachment.content.path));
    } catch (error) {
      console.error("Failed to fetch attachment:", error);
    }
  }

  useEffect(() => {
    fetchPath();
  }, [attachment.content.path]);

  if (!path) return <></>;

  switch (attachment.content.file_type) {
    case "image/jpg":
    case "image/jpeg":
    case "image/png":
    case "image/webp":
      return (
        <span className="w-full h-auto overflow-hidden bg-lime-450/30 rounded-md">
          <img
            src={path}
            width="100%"
            height="100%"
            alt={attachment.content.name}
            className="w-full h-auto max-h-[200px] object-contain rounded-md"
          />
        </span>
      );
    case "application/pdf":
      return <></>;
    default:
      return <p className="text-sm text-gray-500">{attachment.content.name}</p>;
  }
}

function MessageAttachment({ association }: { association: any }) {
  const { isOpen, document, openPreview, closePreview } = useDocumentPreview();

  function handleOpenPreview(attachment: any) {
    if (!attachment.content.path) return;
    openPreview({
      uri: attachment.content.path,
      fileType: attachment.content.file_type,
    });
  }

  return (
    <Fragment>
      <MessageAdormentPill
        className="flex flex-col gap-2 p-2 items-start"
        onClick={() => handleOpenPreview(association)}
      >
        <MessageAttachmentThumbnail attachment={association} />
        <span className="flex flex-row items-center gap-2 p-2">
          <Paperclip size={12} />
          <p className="capitalize font-semibold text-white/60">
            Attachment: {association.content.name}{" "}
          </p>
        </span>
      </MessageAdormentPill>
      {/* Document Preview Dialog */}
      <DocumentPreviewDialog
        isOpen={isOpen}
        onClose={closePreview}
        document={document}
      />
    </Fragment>
  );
}

function MessageAdornment({ association }: { association: any }) {
  switch (association.entity) {
    case "message":
      return <MessageReply association={association} />;
    case "attachment":
      return <MessageAttachment association={association} />;
    default:
      return <></>;
  }
}

function MessageItem({
  message,
  isOwn,
  showAvatar,
  showTimestamp,
  copyMessage,
  deleteMessage,
  updateMessage,
  replyToMessage,
}: MessageItemProps) {
  const handleCopy = () => {
    try {
      copyMessage(message.content);
      toast.success("Message copied to clipboard");
    } catch (error) {
      toast.error("Failed to copy message");
    }
  };

  const handleReply = () => {
    // Use the hook's replyToMessage function directly
    replyToMessage({
      id: message.id,
      content: message.content,
      sender: message.sender,
    });
  };

  const handleEdit = () => {
    // For now, just log the edit action
    updateMessage(message);
  };

  const handleDelete = async () => {
    deleteMessage(message.id);
  };

  const senderName = message.sender?.name || "Unknown User";
  const senderInitials = senderName
    .split(" ")
    .map((n) => n[0])
    .join("")
    .toUpperCase()
    .slice(0, 2);

  return (
    <div
      id={`message-${message.id}`}
      className={cn(
        "flex gap-3 group cursor-pointer transition-colors duration-200",
        isOwn ? "flex-row-reverse" : "flex-row"
      )}
    >
      {/* Avatar */}
      <div
        className={cn(
          "flex-shrink-0",
          showAvatar ? "opacity-100" : "opacity-0"
        )}
      >
        <Avatar className="h-8 w-8">
          <AvatarImage src={message.sender?.avatar} alt={senderName} />
          <AvatarFallback className="text-xs">{senderInitials}</AvatarFallback>
        </Avatar>
      </div>

      {/* Message content */}
      <div
        className={cn(
          "flex flex-col max-w-[70%] gap-2",
          isOwn ? "items-end" : "items-start"
        )}
      >
        {/* Sender name and timestamp */}
        {showTimestamp && (
          <div
            className={cn(
              "flex items-center gap-2 mb-1",
              isOwn ? "flex-row-reverse" : "flex-row"
            )}
          >
            <span className="text-sm font-medium">
              {isOwn ? "You" : senderName}
            </span>
            <span className="text-sm text-gray-500">
              {formatDistanceToNow(new Date(message.createdAt), {
                addSuffix: true,
              })}
            </span>
          </div>
        )}

        {/* Message bubble */}
        <ContextMenu>
          <ContextMenuTrigger asChild>
            <div
              className={cn(
                "rounded-xl p-2 max-w-full break-words",
                isOwn ? "bg-lime-350 text-white" : "bg-gray-100 text-gray-900"
              )}
            >
              {/* Message content */}
              <div className="prose prose-sm max-w-none">
                {HtmlParser(message.content)}
              </div>

              {/* Associations (forms, files, etc.) */}
              {message.associations && message.associations.length > 0 && (
                <div className="mt-2 flex flex-col gap-1">
                  {message.associations.map((association, index) => (
                    <MessageAdornment key={index} association={association} />
                  ))}
                </div>
              )}
            </div>
          </ContextMenuTrigger>
          <ContextMenuContent>
            <ContextMenuItem onClick={handleCopy}>
              <Copy className="mr-2 h-4 w-4" />
              Copy
            </ContextMenuItem>
            <ContextMenuItem onClick={handleReply}>
              <Reply className="mr-2 h-4 w-4" />
              Reply
            </ContextMenuItem>
            {isOwn && (
              <>
                <ContextMenuSeparator />
                <ContextMenuItem onClick={handleEdit}>
                  <Pencil className="mr-2 h-4 w-4" />
                  Edit
                </ContextMenuItem>

                <ContextMenuItem
                  onClick={handleDelete}
                  className="text-red-600"
                >
                  <Trash className="mr-2 h-4 w-4" />
                  Delete
                </ContextMenuItem>
              </>
            )}
          </ContextMenuContent>
        </ContextMenu>

        {/* Timestamp on hover */}
        {!showTimestamp && (
          <span className="text-xs text-gray-500 opacity-0 group-hover:opacity-100 transition-opacity mt-1">
            {formatDistanceToNow(new Date(message.createdAt), {
              addSuffix: true,
            })}
          </span>
        )}
      </div>
    </div>
  );
}

export function MessageList({
  messages,
  currentUserId,
  copyMessage,
  deleteMessage,
  updateMessage,
  replyToMessage,
  isLoading = false,
  className,
}: MessageListProps) {
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const containerRef = useRef<HTMLDivElement>(null);

  // Auto-scroll to bottom when new messages arrive
  useEffect(() => {
    if (messagesEndRef.current) {
      messagesEndRef.current.scrollIntoView({ behavior: "smooth" });
    }
  }, [messages]);

  if (isLoading) {
    return (
      <div className={cn("flex-1 flex items-center justify-center", className)}>
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-2"></div>
          <p className="text-sm text-gray-500">Loading messages...</p>
        </div>
      </div>
    );
  }

  if (messages.length === 0) {
    return (
      <div
        className={cn(
          "flex flex-col items-center justify-center text-center",
          className
        )}
      >
        <p className="text-gray-500 mb-2">No messages yet</p>
        <p className="text-sm text-gray-400">Start a new room</p>
      </div>
    );
  }

  return (
    <div
      ref={containerRef}
      className={cn("flex-1 overflow-y-auto p-4 space-y-4", className)}
    >
      {messages.map((message, index) => {
        const isOwn = message.sent_from === currentUserId;
        const prevMessage = index > 0 ? messages[index - 1] : null;
        const nextMessage =
          index < messages.length - 1 ? messages[index + 1] : null;

        // Show avatar if it's the first message from this sender in a group
        const showAvatar =
          !prevMessage || prevMessage.sent_from !== message.sent_from;

        // Show timestamp if it's been more than 5 minutes since the last message
        // or if it's from a different sender
        const showTimestamp =
          !prevMessage ||
          prevMessage.sent_from !== message.sent_from ||
          new Date(message.createdAt).getTime() -
            new Date(prevMessage.createdAt).getTime() >
            5 * 60 * 1000;

        return (
          <MessageItem
            key={message.id}
            message={message}
            isOwn={isOwn}
            showAvatar={showAvatar}
            copyMessage={copyMessage}
            deleteMessage={deleteMessage}
            updateMessage={updateMessage}
            replyToMessage={replyToMessage}
            showTimestamp={showTimestamp}
          />
        );
      })}

      {/* Scroll anchor */}
      <div ref={messagesEndRef} />
    </div>
  );
}
