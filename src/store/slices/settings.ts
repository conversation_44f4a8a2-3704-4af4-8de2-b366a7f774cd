import { createSlice, PayloadAction } from "@reduxjs/toolkit";

// Notification preferences interface
export interface NotificationPreferences {
  email: {
    newMessages: boolean;
    documentUpdates: boolean;
    contractChanges: boolean;
    proposalUpdates: boolean;
    userActivity: boolean;
    systemAlerts: boolean;
    weeklyDigest: boolean;
  };
  push: {
    newMessages: boolean;
    documentUpdates: boolean;
    contractChanges: boolean;
    proposalUpdates: boolean;
    userActivity: boolean;
    systemAlerts: boolean;
  };
  inApp: {
    newMessages: boolean;
    documentUpdates: boolean;
    contractChanges: boolean;
    proposalUpdates: boolean;
    userActivity: boolean;
    systemAlerts: boolean;
    roleChanges: boolean;
  };
}

// Settings state interface
export interface SettingsState {
  // Layout and theme settings
  layout: "basic" | "advanced";
  theme: "light" | "dark";
  
  // Notification preferences
  notifications: NotificationPreferences;
  
  // Other settings can be added here
  language: string;
  timezone: string;
  dateFormat: string;
  
  // Loading states
  isLoading: boolean;
  isSaving: boolean;
  error: string | null;
}

// Default notification preferences
const defaultNotificationPreferences: NotificationPreferences = {
  email: {
    newMessages: true,
    documentUpdates: true,
    contractChanges: true,
    proposalUpdates: true,
    userActivity: false,
    systemAlerts: true,
    weeklyDigest: true,
  },
  push: {
    newMessages: true,
    documentUpdates: false,
    contractChanges: true,
    proposalUpdates: true,
    userActivity: false,
    systemAlerts: true,
  },
  inApp: {
    newMessages: true,
    documentUpdates: true,
    contractChanges: true,
    proposalUpdates: true,
    userActivity: true,
    systemAlerts: true,
    roleChanges: true,
  },
};

// Initial state
const initialState: SettingsState = {
  layout: "basic",
  theme: "light",
  notifications: defaultNotificationPreferences,
  language: "en",
  timezone: "UTC",
  dateFormat: "MM/dd/yyyy",
  isLoading: false,
  isSaving: false,
  error: null,
};

// Create the settings slice
const settingsSlice = createSlice({
  name: "settings",
  initialState,
  reducers: {
    // Set all settings
    setSettings: (state, action: PayloadAction<Partial<SettingsState>>) => {
      return { ...state, ...action.payload };
    },

    // Set layout preference
    setLayout: (state, action: PayloadAction<"basic" | "advanced">) => {
      state.layout = action.payload;
    },

    // Set theme preference
    setTheme: (state, action: PayloadAction<"light" | "dark">) => {
      state.theme = action.payload;
    },

    // Set notification preferences
    setNotificationPreferences: (state, action: PayloadAction<NotificationPreferences>) => {
      state.notifications = action.payload;
    },

    // Update specific notification preference
    updateNotificationPreference: (
      state,
      action: PayloadAction<{
        category: keyof NotificationPreferences;
        key: string;
        value: boolean;
      }>
    ) => {
      const { category, key, value } = action.payload;
      if (state.notifications[category] && key in state.notifications[category]) {
        (state.notifications[category] as any)[key] = value;
      }
    },

    // Reset notification preferences to defaults
    resetNotificationPreferences: (state) => {
      state.notifications = defaultNotificationPreferences;
    },

    // Set language
    setLanguage: (state, action: PayloadAction<string>) => {
      state.language = action.payload;
    },

    // Set timezone
    setTimezone: (state, action: PayloadAction<string>) => {
      state.timezone = action.payload;
    },

    // Set date format
    setDateFormat: (state, action: PayloadAction<string>) => {
      state.dateFormat = action.payload;
    },

    // Set loading state
    setLoading: (state, action: PayloadAction<boolean>) => {
      state.isLoading = action.payload;
    },

    // Set saving state
    setSaving: (state, action: PayloadAction<boolean>) => {
      state.isSaving = action.payload;
    },

    // Set error
    setError: (state, action: PayloadAction<string | null>) => {
      state.error = action.payload;
    },

    // Clear error
    clearError: (state) => {
      state.error = null;
    },

    // Reset all settings to defaults
    resetSettings: () => {
      return initialState;
    },
  },
});

// Export actions
export const {
  setSettings,
  setLayout,
  setTheme,
  setNotificationPreferences,
  updateNotificationPreference,
  resetNotificationPreferences,
  setLanguage,
  setTimezone,
  setDateFormat,
  setLoading,
  setSaving,
  setError,
  clearError,
  resetSettings,
} = settingsSlice.actions;

// Export selectors
export const selectSettings = (state: { settings: SettingsState }) => state.settings;
export const selectLayout = (state: { settings: SettingsState }) => state.settings.layout;
export const selectTheme = (state: { settings: SettingsState }) => state.settings.theme;
export const selectNotificationPreferences = (state: { settings: SettingsState }) => 
  state.settings.notifications;
export const selectLanguage = (state: { settings: SettingsState }) => state.settings.language;
export const selectTimezone = (state: { settings: SettingsState }) => state.settings.timezone;
export const selectDateFormat = (state: { settings: SettingsState }) => state.settings.dateFormat;
export const selectIsLoading = (state: { settings: SettingsState }) => state.settings.isLoading;
export const selectIsSaving = (state: { settings: SettingsState }) => state.settings.isSaving;
export const selectError = (state: { settings: SettingsState }) => state.settings.error;

// Export reducer
export default settingsSlice.reducer;
