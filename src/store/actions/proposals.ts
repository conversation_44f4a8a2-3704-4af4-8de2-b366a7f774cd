"use client";

import { createAsyncThunk } from "@reduxjs/toolkit";
import { api } from "@/lib/common/requests";
import type {
  CreateProposal,
  UpdateProposal,
} from "@/lib/api/validators/schemas/proposal";

// Fetch all proposals
export const fetchProposals = createAsyncThunk(
  "proposals/fetchProposals",
  async (_, { rejectWithValue }) => {
    try {
      const response = await api.get<any>("proposal");

      if (response.success && !response.error) {
        return response.data;
      } else {
        return rejectWithValue({
          success: false,
          error: true,
          message: response.message || "Failed to fetch proposals",
        });
      }
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : "Failed to fetch proposals";
      return rejectWithValue({
        success: false,
        error: true,
        message: errorMessage,
      });
    }
  }
);

// Fetch single proposal
export const fetchProposal = createAsyncThunk(
  "proposals/fetchProposal",
  async (proposalId: string, { rejectWithValue }) => {
    try {
      const response = await api.get<any>(`proposal?id=${proposalId}`);

      if (response.success && !response.error) {
        return response.data;
      } else {
        return rejectWithValue({
          success: false,
          error: true,
          message: response.message || "Failed to fetch proposal",
        });
      }
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : "Failed to fetch proposal";
      return rejectWithValue({
        success: false,
        error: true,
        message: errorMessage,
      });
    }
  }
);

// Create proposal
export const createProposal = createAsyncThunk(
  "proposals/createProposal",
  async (proposalData: CreateProposal | FormData, { rejectWithValue }) => {
    try {
      let response: any;

      if (proposalData instanceof FormData) {
        response = await api.upload<any>("proposal", proposalData);
      } else {
        response = await api.post<any>("proposal", proposalData);
      }

      if (response.success && !response.error) {
        return response.data;
      } else {
        return rejectWithValue({
          success: false,
          error: true,
          message: response.message || "Failed to create proposal",
        });
      }
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : "Failed to create proposal";
      return rejectWithValue({
        success: false,
        error: true,
        message: errorMessage,
      });
    }
  }
);

// Update proposal
export const updateProposal = createAsyncThunk(
  "proposals/updateProposal",
  async (data: Partial<UpdateProposal>, { rejectWithValue }) => {
    try {
      let response: any;
      if (data instanceof FormData) {
        response = await api.upPatch<any>(`proposal/${data.id}`, data);
      } else {
        response = await api.patch<any>(`proposal/${data.id}`, data);
      }

      if (response.success && !response.error) {
        return response.data;
      } else {
        return rejectWithValue({
          success: false,
          error: true,
          message: response.message || "Failed to update proposal",
        });
      }
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : "Failed to update proposal";
      return rejectWithValue({
        success: false,
        error: true,
        message: errorMessage,
      });
    }
  }
);

// Delete proposal
export const deleteProposal = createAsyncThunk(
  "proposals/deleteProposal",
  async (proposalId: string, { rejectWithValue }) => {
    try {
      const response = await api.delete<any>(`proposal/${proposalId}`);

      if (response.success && !response.error) {
        return proposalId;
      } else {
        return rejectWithValue({
          success: false,
          error: true,
          message: response.message || "Failed to delete proposal",
        });
      }
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : "Failed to delete proposal";
      return rejectWithValue({
        success: false,
        error: true,
        message: errorMessage,
      });
    }
  }
);

// Fetch proposal statistics
export const fetchProposalStatistics = createAsyncThunk(
  "proposals/fetchStatistics",
  async (_, { rejectWithValue }) => {
    try {
      const response = await api.get<any>("proposal/statistics");

      if (response.success && !response.error) {
        return response.data;
      } else {
        return rejectWithValue({
          success: false,
          error: true,
          message: response.message || "Failed to fetch proposal statistics",
        });
      }
    } catch (error) {
      const errorMessage =
        error instanceof Error
          ? error.message
          : "Failed to fetch proposal statistics";
      return rejectWithValue({
        success: false,
        error: true,
        message: errorMessage,
      });
    }
  }
);
