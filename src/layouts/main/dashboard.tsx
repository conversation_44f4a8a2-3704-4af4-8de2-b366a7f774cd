"use client";

import React from "react";
import { AppSidebar } from "@/components/common/layout/AppSidebar";
import {
  SidebarInset,
  SidebarHeader,
  SidebarContent,
  SidebarProvider,
  SidebarTrigger,
} from "@/components/common/ui/sidebar";
import { Separator } from "@/components/common/ui/separator";
import { ThemeToggle } from "@/components/common/ui/theme-toggle";

interface DashboardLayoutProps {
  children: React.ReactNode;
}

export default function Layout({ children }: DashboardLayoutProps) {
  return (
    <SidebarProvider defaultOpen={true}>
      <AppSidebar />
      <SidebarInset>
        <SidebarHeader className="flex h-16 shrink-0 items-center gap-2 transition-[width,height] ease-linear group-has-[[data-collapsible=icon]]/sidebar-wrapper:h-12 bg-background border-b">
          <div className="flex items-center gap-2 px-4 w-full">
            <SidebarTrigger className="-ml-1" />
            <Separator orientation="vertical" className="mr-2 h-4" />
            <ThemeToggle />
          </div>
        </SidebarHeader>
        <SidebarContent>{children}</SidebarContent>
      </SidebarInset>
    </SidebarProvider>
  );
}
