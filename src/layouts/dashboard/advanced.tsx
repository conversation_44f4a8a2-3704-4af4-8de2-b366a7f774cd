"use client";

import React from "react";
import {
  Search,
  Filter,
  Grid3X3,
  List,
  Table,
  FileText,
  Folder,
  File,
  Image,
  Video,
  Music,
  Archive,
  Code,
  Database,
  BarChart3,
  X,
  Network,
} from "lucide-react";
import { <PERSON><PERSON> } from "@/components/common/ui/button";
import { Input } from "@/components/common/ui/input";
import { Badge } from "@/components/common/ui/badge";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/common/ui/dropdown-menu";
import {
  Drawer,
  DrawerContent,
  DrawerHeader,
  DrawerTitle,
  DrawerDescription,
  DrawerClose,
} from "@/components/common/ui/drawer";
import { FlowGrid } from "@/components/common/flow";
import {
  ContextMenu,
  ContextMenuContent,
  ContextMenuItem,
  ContextMenuSeparator,
  ContextMenuTrigger,
} from "@/components/common/ui/context-menu";

// Types for the advanced layout
export type ViewMode = "table" | "grid" | "list" | "flow";
export type DataType =
  | "file"
  | "folder"
  | "document"
  | "image"
  | "video"
  | "audio"
  | "archive"
  | "code"
  | "database"
  | "default";

export interface InsightsWidget {
  id: string;
  title: string;
  value: string | number;
  description?: string;
  icon?: React.ReactNode;
  trend?: {
    value: number;
    isPositive: boolean;
  };
}

export interface ToolbarProps {
  searchValue?: string;
  onSearchChange?: (value: string) => void;
  searchPlaceholder?: string;
  quickFilters?: Array<{
    label: string;
    value: string;
    count?: number;
    active?: boolean;
    onClick?: () => void;
  }>;
  viewMode?: ViewMode;
  onViewModeChange?: (mode: ViewMode) => void;
  enabledViewModes?: ViewMode[];
  className?: string;
  // Insights drawer props
  showInsightsButton?: boolean;
  insightsWidgets?: InsightsWidget[];
  onInsightsToggle?: (open: boolean) => void;
}

export interface AdvancedListingProps {
  children?: React.ReactNode;
  className?: string;
  toolbar?: ToolbarProps;
  hideStatistics?: boolean; // Option to hide statistics from main view
}

export interface HeaderProps {
  title: string;
  caption?: string;
  actions?: React.ReactNode;
  className?: string;
}

// Icon mapping based on data type
const getIconForDataType = (type: DataType, size: number = 16) => {
  const iconProps = { size };

  switch (type) {
    case "file":
      return <File {...iconProps} />;
    case "folder":
      return <Folder {...iconProps} />;
    case "document":
      return <FileText {...iconProps} />;
    case "image":
      return <Image {...iconProps} />;
    case "video":
      return <Video {...iconProps} />;
    case "audio":
      return <Music {...iconProps} />;
    case "archive":
      return <Archive {...iconProps} />;
    case "code":
      return <Code {...iconProps} />;
    case "database":
      return <Database {...iconProps} />;
    default:
      return <File {...iconProps} />;
  }
};

// Insights Drawer Component
function InsightsDrawer({
  widgets = [],
  open,
  onOpenChange,
}: {
  widgets: InsightsWidget[];
  open: boolean;
  onOpenChange: (open: boolean) => void;
}) {
  return (
    <Drawer open={open} onOpenChange={onOpenChange} direction="right">
      <DrawerContent className="w-96 max-w-[90vw]">
        <DrawerHeader className="border-b">
          <div className="flex items-center justify-between">
            <div>
              <DrawerTitle className="flex items-center gap-2">
                <BarChart3 size={20} />
                Insights
              </DrawerTitle>
              <DrawerDescription>
                Key metrics and statistics overview
              </DrawerDescription>
            </div>
            <DrawerClose asChild>
              <Button variant="ghost" size="sm">
                <X size={16} />
              </Button>
            </DrawerClose>
          </div>
        </DrawerHeader>

        <div className="flex-1 overflow-auto p-4">
          <div className="space-y-4">
            {widgets.map((widget) => (
              <div
                key={widget.id}
                className="p-4 border rounded-lg bg-card hover:bg-accent/50 transition-colors"
              >
                <div className="flex items-start justify-between mb-2">
                  <div className="flex items-center gap-2">
                    {widget.icon}
                    <h3 className="font-medium text-sm">{widget.title}</h3>
                  </div>
                  {widget.trend && (
                    <div
                      className={`text-xs px-2 py-1 rounded-full ${
                        widget.trend.isPositive
                          ? "bg-green-100 text-green-700 dark:bg-green-900/20 dark:text-green-400"
                          : "bg-red-100 text-red-700 dark:bg-red-900/20 dark:text-red-400"
                      }`}
                    >
                      {widget.trend.isPositive ? "+" : ""}
                      {widget.trend.value}%
                    </div>
                  )}
                </div>
                <div className="text-2xl font-bold mb-1">{widget.value}</div>
                {widget.description && (
                  <p className="text-sm text-muted-foreground">
                    {widget.description}
                  </p>
                )}
              </div>
            ))}

            {widgets.length === 0 && (
              <div className="text-center py-8 text-muted-foreground">
                <BarChart3 className="mx-auto h-12 w-12 mb-4 opacity-50" />
                <p>No insights available</p>
                <p className="text-sm">
                  Statistics will appear here when data is loaded
                </p>
              </div>
            )}
          </div>
        </div>
      </DrawerContent>
    </Drawer>
  );
}

// Toolbar component
function Toolbar({
  searchValue = "",
  onSearchChange,
  searchPlaceholder = "Search...",
  quickFilters = [],
  viewMode = "table",
  onViewModeChange,
  enabledViewModes = ["table", "grid", "list"],
  className = "",
  showInsightsButton = true,
  insightsWidgets = [],
  onInsightsToggle,
}: ToolbarProps) {
  const [insightsOpen, setInsightsOpen] = React.useState(false);

  const handleInsightsToggle = (open: boolean) => {
    setInsightsOpen(open);
    onInsightsToggle?.(open);
  };
  const viewModeIcons = {
    table: Table,
    grid: Grid3X3,
    list: List,
    flow: Network,
  };

  return (
    <>
      <div
        className={`flex flex-col sm:flex-row gap-4 pb-6 border-b bg-background ${className}`}
      >
        {/* Left side - Search and Quick Filters */}
        <div className="flex-1 flex flex-col sm:flex-row gap-4">
          {/* Search */}
          <div className="relative flex-1 max-w-md">
            <Search
              className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground"
              size={16}
            />
            <Input
              type="text"
              placeholder={searchPlaceholder}
              value={searchValue}
              onChange={(e) => onSearchChange?.(e.target.value)}
              className="pl-10"
            />
          </div>

          {/* Quick Filters */}
          {quickFilters.length > 0 && (
            <div className="flex flex-wrap gap-2 items-center">
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="outline" size="sm" className="gap-2">
                    <Filter size={16} />
                    Filters
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="start" className="w-56">
                  {quickFilters.map((filter, index) => (
                    <DropdownMenuItem
                      key={index}
                      onClick={filter.onClick}
                      className={filter.active ? "bg-accent" : ""}
                    >
                      <div className="flex items-center justify-between w-full">
                        <span>{filter.label}</span>
                        {filter.count !== undefined && (
                          <Badge variant="secondary" className="ml-2">
                            {filter.count}
                          </Badge>
                        )}
                      </div>
                    </DropdownMenuItem>
                  ))}
                </DropdownMenuContent>
              </DropdownMenu>

              {/* Active filters as badges */}
              {quickFilters
                .filter((filter) => filter.active)
                .map((filter, index) => (
                  <Badge
                    key={index}
                    variant="default"
                    className="cursor-pointer"
                    onClick={filter.onClick}
                  >
                    {filter.label}
                    {filter.count !== undefined && ` (${filter.count})`}
                  </Badge>
                ))}
            </div>
          )}
        </div>

        {/* Right side - Insights Button and View Mode Toggle */}
        <div className="flex gap-2 items-center">
          {/* Insights Button */}
          {showInsightsButton && (
            <Button
              variant="outline"
              size="sm"
              onClick={() => handleInsightsToggle(true)}
              className="gap-2 h-full"
            >
              <BarChart3 size={16} />
              <span className="hidden sm:inline">Insights</span>
            </Button>
          )}

          {/* View Mode Toggle */}
          <div className="flex gap-1 rounded-md">
            {enabledViewModes.map((mode) => {
              const IconComponent = viewModeIcons[mode];
              return (
                <Button
                  key={mode}
                  variant={viewMode === mode ? "default" : "ghost"}
                  size="sm"
                  onClick={() => onViewModeChange?.(mode)}
                  className="px-3"
                >
                  <IconComponent size={16} />
                </Button>
              );
            })}
          </div>
        </div>
      </div>

      {/* Insights Drawer */}
      <InsightsDrawer
        widgets={insightsWidgets}
        open={insightsOpen}
        onOpenChange={handleInsightsToggle}
      />
    </>
  );
}

// Header Component for Advanced Layout
function Header({ title, caption, actions, className }: HeaderProps) {
  return (
    <div
      className={`flex flex-wrap items-center justify-between pb-6 border-b bg-background ${className}`}
    >
      <div className="flex flex-col gap-1">
        <h1 className="text-2xl font-bold text-foreground">{title}</h1>
        {caption && <p className="text-sm text-muted-foreground">{caption}</p>}
      </div>
      {actions && <div className="flex items-center gap-2">{actions}</div>}
    </div>
  );
}

// Main Advanced Listing component
function AdvancedListing({
  children,
  className = "",
  toolbar,
  hideStatistics = false,
}: AdvancedListingProps) {
  return (
    <div className={`overflow-hidden ${className}`}>
      {toolbar && <Toolbar {...toolbar} />}
      <div className="flex-1">{children}</div>
    </div>
  );
}

// Enhanced Table component with dynamic icons
interface AdvancedTableProps {
  id?: string;
  data?: any[];
  columns: Array<{
    key: string;
    label: string;
    render?: (item: any, index: number) => React.ReactNode;
    className?: string;
    dataType?: DataType; // New prop for dynamic icons
  }>;
  loading?: boolean;
  emptyState?: React.ReactNode;
  onRowClick?: (item: any) => void;
  className?: string;
  enableCheckboxes?: boolean;
  selectedRowIds?: string[];
  onSelectionChange?: (selectedIds: string[]) => void;
  getRowId?: (item: any) => string;
  getDataType?: (item: any, columnKey: string) => DataType; // Function to determine data type
  contextMenuActions?: Array<{
    id: string;
    label: string;
    icon?: React.ReactNode;
    onClick: (item: any) => void;
    disabled?: boolean;
    separator?: boolean;
  }>;
}

function AdvancedTable({
  id = "advanced-table",
  data = [],
  columns,
  loading = false,
  emptyState,
  onRowClick,
  className = "",
  enableCheckboxes = false,
  selectedRowIds = [],
  onSelectionChange,
  getRowId = (item) => item.id,
  getDataType,
  contextMenuActions = [],
}: AdvancedTableProps) {
  const renderCellContent = (item: any, column: any, index: number) => {
    if (column.render) {
      return column.render(item, index);
    }

    const cellValue = item[column.key];

    // Determine data type for dynamic icons
    let dataType: DataType = "default";
    if (getDataType) {
      dataType = getDataType(item, column.key);
    } else if (column.dataType) {
      dataType = column.dataType;
    } else {
      // Auto-detect based on common patterns
      if (column.key.includes("name") || column.key.includes("title")) {
        if (item.type === "folder" || item.folder) dataType = "folder";
        else if (item.type === "file" || item.file_type) {
          const fileType = item.file_type || item.type;
          if (fileType?.includes("image")) dataType = "image";
          else if (fileType?.includes("video")) dataType = "video";
          else if (fileType?.includes("audio")) dataType = "audio";
          else if (fileType?.includes("archive") || fileType?.includes("zip"))
            dataType = "archive";
          else if (
            fileType?.includes("code") ||
            fileType?.includes("javascript") ||
            fileType?.includes("typescript")
          )
            dataType = "code";
          else if (fileType?.includes("database") || fileType?.includes("sql"))
            dataType = "database";
          else if (
            fileType?.includes("document") ||
            fileType?.includes("pdf") ||
            fileType?.includes("doc")
          )
            dataType = "document";
          else dataType = "file";
        }
      }
    }

    // Render with icon if it's a name/title column
    if (
      (column.key.includes("name") || column.key.includes("title")) &&
      dataType !== "default"
    ) {
      return (
        <div className="flex items-center gap-2">
          {getIconForDataType(dataType, 16)}
          <span>{cellValue}</span>
        </div>
      );
    }

    return cellValue;
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="text-muted-foreground">Loading...</div>
      </div>
    );
  }

  if (!data.length && emptyState) {
    return <div className="p-8">{emptyState}</div>;
  }

  return (
    <div className={`overflow-auto ${className}`}>
      <table className="w-full">
        <thead className="border-b bg-muted/50">
          <tr>
            {enableCheckboxes && (
              <th className="w-12 p-4">
                <input
                  type="checkbox"
                  checked={
                    selectedRowIds.length === data.length && data.length > 0
                  }
                  onChange={(e) => {
                    if (e.target.checked) {
                      onSelectionChange?.(data.map(getRowId));
                    } else {
                      onSelectionChange?.([]);
                    }
                  }}
                />
              </th>
            )}
            {columns.map((column) => (
              <th
                key={column.key}
                className={`text-left p-4 font-medium text-muted-foreground ${
                  column.className || ""
                }`}
              >
                {column.label}
              </th>
            ))}
          </tr>
        </thead>
        <tbody>
          {data.map((item, index) => {
            const rowId = getRowId(item);
            const isSelected = selectedRowIds.includes(rowId);

            const rowContent = (
              <tr
                key={rowId}
                className={`border-b hover:bg-muted/50 cursor-pointer ${
                  isSelected ? "bg-accent" : ""
                }`}
                onClick={() => onRowClick?.(item)}
              >
                {enableCheckboxes && (
                  <td className="p-4">
                    <input
                      type="checkbox"
                      checked={isSelected}
                      onChange={(e) => {
                        e.stopPropagation();
                        if (e.target.checked) {
                          onSelectionChange?.([...selectedRowIds, rowId]);
                        } else {
                          onSelectionChange?.(
                            selectedRowIds.filter((id) => id !== rowId)
                          );
                        }
                      }}
                    />
                  </td>
                )}
                {columns.map((column) => (
                  <td
                    key={column.key}
                    className={`p-4 ${column.className || ""}`}
                  >
                    {renderCellContent(item, column, index)}
                  </td>
                ))}
              </tr>
            );

            // If no context menu actions, return row without context menu
            if (!contextMenuActions || contextMenuActions.length === 0) {
              return rowContent;
            }

            return (
              <ContextMenu key={rowId}>
                <ContextMenuTrigger asChild>{rowContent}</ContextMenuTrigger>
                <ContextMenuContent className="w-48">
                  {contextMenuActions.map((action, actionIndex) => (
                    <React.Fragment key={action.id}>
                      {action.separator && actionIndex > 0 && (
                        <ContextMenuSeparator />
                      )}
                      <ContextMenuItem
                        onClick={() => action.onClick(item)}
                        disabled={action.disabled}
                        className="flex items-center gap-2"
                      >
                        {action.icon}
                        <span>{action.label}</span>
                      </ContextMenuItem>
                    </React.Fragment>
                  ))}
                </ContextMenuContent>
              </ContextMenu>
            );
          })}
        </tbody>
      </table>
    </div>
  );
}

// Grid View component for card-based layout
interface AdvancedGridProps {
  data?: any[];
  loading?: boolean;
  emptyState?: React.ReactNode;
  onItemClick?: (item: any) => void;
  className?: string;
  getDataType?: (item: any) => DataType;
  renderCard?: (item: any, index: number) => React.ReactNode;
  contextMenuActions?: Array<{
    id: string;
    label: string;
    icon?: React.ReactNode;
    onClick: (item: any) => void;
    disabled?: boolean;
    separator?: boolean;
  }>;
}

function AdvancedGrid({
  data = [],
  loading = false,
  emptyState,
  onItemClick,
  className = "",
  getDataType,
  renderCard,
  contextMenuActions = [],
}: AdvancedGridProps) {
  if (loading) {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="text-muted-foreground">Loading...</div>
      </div>
    );
  }

  if (!data.length && emptyState) {
    return <div className="p-8">{emptyState}</div>;
  }

  const Thumbnail = ({ item }: { item: any }) => {
    return (
      <object
        className="w-full h-full"
        data={item.thumbnail}
        type={item.mime_type}
      ></object>
    );
  };

  const defaultRenderCard = (item: any, index: number) => {
    const dataType = getDataType ? getDataType(item) : "default";
    const name = item.name || item.title || `Item ${index + 1}`;

    const cardContent = (
      <div
        key={item.id || index}
        className="hover:bg-muted/50 cursor-pointer transition-colors"
        onClick={() => onItemClick?.(item)}
      >
        {/* Parent Component: flex-col gap-1 */}
        <div className="flex flex-col gap-2">
          {/* Thumbnail or Folder Icon */}
          <div className="flex justify-center items-center h-16 w-full bg-muted/30 rounded-md">
            {item?.path ? (
              <Thumbnail item={item} />
            ) : (
              getIconForDataType(dataType, 32)
            )}
          </div>
          {/* Icon + Filename: flex flex-row justified items-center */}
          <span className="font-medium truncate text-sm py-1 px-2">{name}</span>
        </div>
      </div>
    );

    // If no context menu actions, return card without context menu
    if (!contextMenuActions || contextMenuActions.length === 0) {
      return cardContent;
    }

    return (
      <ContextMenu key={item.id || index}>
        <ContextMenuTrigger asChild>{cardContent}</ContextMenuTrigger>
        <ContextMenuContent className="w-48">
          {contextMenuActions.map((action, actionIndex) => (
            <React.Fragment key={action.id}>
              {action.separator && actionIndex > 0 && <ContextMenuSeparator />}
              <ContextMenuItem
                onClick={() => action.onClick(item)}
                disabled={action.disabled}
                className="flex items-center gap-2"
              >
                {action.icon}
                <span>{action.label}</span>
              </ContextMenuItem>
            </React.Fragment>
          ))}
        </ContextMenuContent>
      </ContextMenu>
    );
  };

  return (
    <div
      className={`grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-6 gap-4 py-6 ${className}`}
    >
      {data.map((item, index) =>
        renderCard ? renderCard(item, index) : defaultRenderCard(item, index)
      )}
    </div>
  );
}

// Advanced Flow component using xyflow/react
export interface AdvancedFlowProps {
  data?: any[];
  loading?: boolean;
  emptyState?: React.ReactNode;
  onItemClick?: (item: any) => void;
  className?: string;
  getDataType?: (item: any) => DataType;
  contextMenuActions?: Array<{
    id: string;
    label: string;
    icon?: React.ReactNode;
    onClick: (item: any) => void;
    disabled?: boolean;
    separator?: boolean;
  }>;
}

function AdvancedFlow({
  data = [],
  loading = false,
  emptyState,
  onItemClick,
  className = "",
  getDataType,
  contextMenuActions = [],
}: AdvancedFlowProps) {
  return (
    <FlowGrid
      data={data}
      loading={loading}
      emptyState={emptyState}
      onItemClick={onItemClick}
      className={className}
      getDataType={getDataType}
      contextMenuActions={contextMenuActions}
    />
  );
}

// Utility function to check if statistics should be shown in main view
export const shouldShowStatisticsInMainView = (
  layoutType: string = "basic"
) => {
  return layoutType === "basic";
};

// Export the advanced layout components
export const Advanced = Object.assign(AdvancedListing, {
  Header,
  Table: AdvancedTable,
  Grid: AdvancedGrid,
  Flow: AdvancedFlow,
  Toolbar,
  InsightsDrawer,
  getIconForDataType,
  shouldShowStatisticsInMainView,
});
