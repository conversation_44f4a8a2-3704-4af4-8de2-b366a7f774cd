import nodemailer from "nodemailer";

/**
 * Email service configuration
 */
interface EmailConfig {
  host: string;
  port: number;
  secure: boolean;
  auth: {
    user: string;
    pass: string;
  };
}

/**
 * Email options interface
 */
export interface EmailOptions {
  to: string | string[];
  cc?: string | string[];
  bcc?: string | string[];
  subject: string;
  html: string;
  text?: string;
  attachments?: Array<{
    filename: string;
    content: Buffer | string;
    contentType?: string;
  }>;
}

/**
 * Email service for sending emails using nodemailer
 */
export class EmailService {
  private transporter: nodemailer.Transporter;
  private fromEmail: string;
  private fromName: string;
  private serviceName: string = "EmailService";

  constructor() {
    // Get email configuration from environment variables
    const config: EmailConfig = {
      host: process.env.SMTP_HOST!,
      port: parseInt(process.env.SMTP_PORT!),
      secure: process.env.SMTP_SECURE === "true",
      auth: {
        user: process.env.SMTP_USER!,
        pass: process.env.SMTP_PASS!,
      },
    };

    this.fromEmail = process.env.FROM_EMAIL || "<EMAIL>";
    this.fromName = process.env.FROM_NAME || "Underscore International";

    // Create nodemailer transporter
    this.transporter = nodemailer.createTransport(config);

    this.log("info", "Email service initialized", {
      host: config.host,
      port: config.port,
      secure: config.secure,
      fromEmail: this.fromEmail,
    });
  }

  /**
   * Internal logging method
   */
  private log(
    level: "info" | "error" | "warn",
    message: string,
    data?: any
  ): void {
    const timestamp = new Date().toISOString();
    const logData = data ? JSON.stringify(data) : "";

    switch (level) {
      case "error":
        console.error(
          `[${timestamp}] ${this.serviceName} ERROR: ${message}`,
          logData
        );
        break;
      case "warn":
        console.warn(
          `[${timestamp}] ${this.serviceName} WARN: ${message}`,
          logData
        );
        break;
      default:
        console.log(
          `[${timestamp}] ${this.serviceName} INFO: ${message}`,
          logData
        );
    }
  }

  /**
   * Send an email
   */
  async sendEmail(options: EmailOptions): Promise<void> {
    try {
      this.log("info", "Sending email", {
        to: options.to,
        subject: options.subject,
        hasAttachments: !!options.attachments?.length,
      });

      const mailOptions = {
        from: `${this.fromName} <${this.fromEmail}>`,
        to: Array.isArray(options.to) ? options.to.join(", ") : options.to,
        cc: options.cc
          ? Array.isArray(options.cc)
            ? options.cc.join(", ")
            : options.cc
          : undefined,
        bcc: options.bcc
          ? Array.isArray(options.bcc)
            ? options.bcc.join(", ")
            : options.bcc
          : undefined,
        subject: options.subject,
        html: options.html,
        text: options.text,
        attachments: options.attachments,
      };

      const result = await this.transporter.sendMail(mailOptions);

      this.log("info", "Email sent successfully", {
        messageId: result.messageId,
        to: options.to,
        subject: options.subject,
      });
    } catch (error) {
      this.log("error", "Failed to send email", {
        to: options.to,
        subject: options.subject,
        error: error instanceof Error ? error.message : String(error),
      });
      throw error;
    }
  }

  /**
   * Verify email configuration
   */
  async verifyConnection(): Promise<boolean> {
    try {
      this.log("info", "Verifying email connection");

      const isConnected = await this.transporter.verify();

      if (isConnected) {
        this.log("info", "Email connection verified successfully");
      } else {
        this.log("error", "Email connection verification failed");
      }

      return isConnected;
    } catch (error) {
      this.log("error", "Email connection verification failed", {
        error: error instanceof Error ? error.message : String(error),
      });
      return false;
    }
  }

  /**
   * Send welcome email
   */
  async sendWelcomeEmail(to: string, userName: string): Promise<void> {
    const { generateWelcomeTemplate } = require("./templates/welcome");

    const html = generateWelcomeTemplate({ userName });

    await this.sendEmail({
      to,
      subject: "Welcome to Underscore International",
      html,
      text: `Welcome to Underscore International, ${userName}! We're excited to have you on board.`,
    });
  }

  /**
   * Send contract notification email
   */
  async sendContractNotification(
    to: string,
    contractData: {
      contractTitle: string;
      clientName: string;
      totalValue: number;
      status: string;
    }
  ): Promise<void> {
    const {
      generateContractNotificationTemplate,
    } = require("./templates/contract-notification");

    const html = generateContractNotificationTemplate(contractData);

    await this.sendEmail({
      to,
      subject: `Contract Update: ${contractData.contractTitle}`,
      html,
      text: `Contract "${contractData.contractTitle}" has been updated. Status: ${contractData.status}`,
    });
  }

  /**
   * Send payment confirmation email
   */
  async sendPaymentConfirmation(
    to: string,
    paymentData: {
      transactionId: string;
      amount: number;
      contractTitle: string;
      paymentDate: string;
    }
  ): Promise<void> {
    const {
      generatePaymentConfirmationTemplate,
    } = require("./templates/payment-confirmation");

    const html = generatePaymentConfirmationTemplate(paymentData);

    await this.sendEmail({
      to,
      subject: `Payment Confirmation - ${paymentData.transactionId}`,
      html,
      text: `Payment of $${paymentData.amount} for "${paymentData.contractTitle}" has been processed successfully.`,
    });
  }

  /**
   * Send password reset email
   */
  async sendPasswordResetEmail(
    to: string,
    resetToken: string,
    userName: string
  ): Promise<void> {
    const {
      generatePasswordResetTemplate,
    } = require("./templates/password-reset");

    const resetUrl = `${process.env.NEXT_PUBLIC_APP_URL}/reset-password?token=${resetToken}`;
    const html = generatePasswordResetTemplate({ userName, resetUrl });

    await this.sendEmail({
      to,
      subject: "Password Reset Request",
      html,
      text: `Hi ${userName}, you requested a password reset. Click this link to reset your password: ${resetUrl}`,
    });
  }
}

// Export singleton instance
export const emailService = new EmailService();
