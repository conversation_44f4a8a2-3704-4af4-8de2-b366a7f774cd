import NextAuth from "next-auth";
import { PrismaAdapter } from "@auth/prisma-adapter";
import { AuthService } from "@/lib/api/services/auth";
import { prisma } from "@/lib/common/prisma";

import type { NextAuthConfig } from "next-auth";
import Google from "next-auth/providers/google";
import LinkedIn from "next-auth/providers/linkedin";
import Credentials from "next-auth/providers/credentials";

const authOptions: NextAuthConfig = {
  adapter: PrismaAdapter(prisma),
  session: { strategy: "jwt" },
  providers: [
    Credentials({
      credentials: {
        username: {},
        password: {},
      },
      async authorize(
        credentials: Record<string, string> | undefined,
        request: Request
      ) {
        const authService = new AuthService();
        const { email, password } = credentials || {};
        const response = await authService.login(email, password);

        if (response?.success) return response?.data;

        return null;
      },
    }),
    Google({
      clientId: process.env.GOOGLE_ID!,
      clientSecret: process.env.GOOGLE_SECRET!,
    }),
    LinkedIn({
      clientId: process.env.LINKEDIN_ID!,
      clientSecret: process.env.LINKEDIN_SECRET!,
    }),
  ],
  callbacks: {
    async jwt({ token, user }: { token: any; user: any }) {
      if (user) {
        token.id = user.id;
        token.name = user.name;
        token.image = user.image;
        token.email = user.email;
        token.role = user.role;
        token.customerId = user.customerId;
        token.createdAt = user.createdAt;
        token.updatedAt = user.updatedAt;
        token.profile = user.profile;
        token.account = user.account;
        token.payment_methods = user.payment_methods;
      }
      return token;
    },
    async session({ session, token }: { session: any; token: any }) {
      if (token) {
        session.user.id = token.id as string;
        session.user.name = token.name as string;
        session.user.image = token.image as string;
        session.user.role = token.role as string;
        session.user.profile = token.profile as string;
        session.user.account = token.account as string;
        session.user.payment_methods = token.payment_methods;
        session.user.customerId = token.customerId as string;
        session.user.createdAt = token.createdAt as string;
        session.user.updatedAt = token.updatedAt as string;
      }
      return session;
    },
  },
  debug: true,
  secret: process.env.NEXTAUTH_SECRET,
  pages: {
    signIn: "/signin",
    signOut: "/signout",
    error: "/error",
  },
};

export const { handlers, auth, signIn, signOut } = NextAuth(authOptions);
