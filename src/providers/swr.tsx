"use client";

import { SWRConfig, SWRConfiguration } from "swr";

export function SWRProvider({ children }: { children: React.ReactNode }) {
  const options: SWRConfiguration = {
    // refreshInterval: 10000,
    // revalidateIfStale: false,
    // revalidateOnFocus: true,
    // revalidateOnReconnect: true,
    fetcher: (resource, init) =>
      fetch(resource, init).then((res) => res.json()),
  };

  return <SWRConfig value={options}>{children}</SWRConfig>;
}
